import { Router } from 'express';
import { User, PermissionTemplate } from '@rxy/database';
import { authenticate, requireSuperAdmin, requireAdmin, canManageUsers } from '../middleware/auth';
import { validateBody } from '../middleware/validation';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { emailService } from '../services/emailService';
import { HTTP_STATUS, SUCCESS_MESSAGES, ERROR_MESSAGES } from '@rxy/shared';

const router = Router();

// Get all users (Super Admin only)
router.get('/',
  authenticate,
  requireSuperAdmin,
  asyncHandler(async (req: any, res: any) => {
    const { page = 1, limit = 10, search, role, status } = req.query;
    
    const query: any = {};
    
    if (search) {
      query.$or = [
        { name: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } },
      ];
    }
    
    if (role && role !== 'all') {
      query.role = role;
    }
    
    if (status && status !== 'all') {
      query.status = status;
    }

    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    const [users, total] = await Promise.all([
      User.find(query)
        .select('-password -refreshTokens -passwordHistory')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(parseInt(limit)),
      User.countDocuments(query)
    ]);

    res.json({
      success: true,
      data: {
        users,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / parseInt(limit)),
        },
      },
    });
  })
);

// Get user by ID
router.get('/:id',
  authenticate,
  requireAdmin,
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    
    const user = await User.findById(id).select('-password -refreshTokens -passwordHistory');
    if (!user) {
      throw createError('User not found', HTTP_STATUS.NOT_FOUND);
    }

    // Check if current user can view this user
    if (!req.user.isSuperAdmin && req.user.id !== id) {
      throw createError('Access denied', HTTP_STATUS.FORBIDDEN);
    }

    res.json({
      success: true,
      data: { user },
    });
  })
);

// Update user
router.put('/:id',
  authenticate,
  requireSuperAdmin,
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    const { firstName, lastName, email, role, status, permissions } = req.body;
    
    const user = await User.findById(id);
    if (!user) {
      throw createError('User not found', HTTP_STATUS.NOT_FOUND);
    }

    // Prevent super admin from demoting themselves
    if (user.isSuperAdmin && req.user.id === id && role !== 'super_admin') {
      throw createError('Cannot demote yourself from Super Admin', HTTP_STATUS.BAD_REQUEST);
    }

    // Update user fields
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;
    if (firstName || lastName) user.name = `${user.firstName} ${user.lastName}`;
    if (email && email !== user.email) {
      // Check if email is already taken
      const existingUser = await User.findByEmail(email);
      if (existingUser && existingUser._id !== id) {
        throw createError('Email already in use', HTTP_STATUS.CONFLICT);
      }
      user.email = email;
      user.emailVerified = false; // Require re-verification
    }
    if (role && ['super_admin', 'admin', 'editor'].includes(role)) {
      user.role = role;
      user.isSuperAdmin = role === 'super_admin';
    }
    if (status && ['active', 'inactive', 'suspended'].includes(status)) {
      user.status = status;
    }
    if (permissions && Array.isArray(permissions)) {
      user.permissions = permissions;
    }

    await user.save();

    res.json({
      success: true,
      message: 'User updated successfully',
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          status: user.status,
          permissions: user.permissions,
          emailVerified: user.emailVerified,
        },
      },
    });
  })
);

// Delete user
router.delete('/:id',
  authenticate,
  requireSuperAdmin,
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    
    const user = await User.findById(id);
    if (!user) {
      throw createError('User not found', HTTP_STATUS.NOT_FOUND);
    }

    // Prevent super admin from deleting themselves
    if (user.isSuperAdmin && req.user.id === id) {
      throw createError('Cannot delete your own Super Admin account', HTTP_STATUS.BAD_REQUEST);
    }

    // Prevent deleting the first user (original super admin)
    if (user.isFirstUser) {
      throw createError('Cannot delete the original Super Admin account', HTTP_STATUS.BAD_REQUEST);
    }

    await User.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'User deleted successfully',
    });
  })
);

// Get available permissions
router.get('/permissions/templates',
  authenticate,
  requireSuperAdmin,
  asyncHandler(async (req: any, res: any) => {
    const permissions = await PermissionTemplate.find().sort({ resource: 1, level: 1 });
    
    res.json({
      success: true,
      data: { permissions },
    });
  })
);

// Update user permissions
router.put('/:id/permissions',
  authenticate,
  requireSuperAdmin,
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    const { permissions } = req.body;
    
    const user = await User.findById(id);
    if (!user) {
      throw createError('User not found', HTTP_STATUS.NOT_FOUND);
    }

    if (!Array.isArray(permissions)) {
      throw createError('Permissions must be an array', HTTP_STATUS.BAD_REQUEST);
    }

    user.permissions = permissions;
    await user.save();

    res.json({
      success: true,
      message: 'User permissions updated successfully',
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          permissions: user.permissions,
        },
      },
    });
  })
);

// Promote user to admin
router.post('/:id/promote',
  authenticate,
  requireSuperAdmin,
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    const { role = 'admin' } = req.body;
    
    const user = await User.findById(id);
    if (!user) {
      throw createError('User not found', HTTP_STATUS.NOT_FOUND);
    }

    if (!['admin', 'super_admin'].includes(role)) {
      throw createError('Invalid role for promotion', HTTP_STATUS.BAD_REQUEST);
    }

    // Get default permissions for the new role
    const defaultPermissions = await PermissionTemplate.getDefaultPermissions(role);
    
    user.role = role;
    user.isSuperAdmin = role === 'super_admin';
    user.permissions = defaultPermissions.map(p => ({
      resource: p.resource,
      actions: p.actions
    }));
    
    await user.save();

    res.json({
      success: true,
      message: `User promoted to ${role} successfully`,
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          isSuperAdmin: user.isSuperAdmin,
          permissions: user.permissions,
        },
      },
    });
  })
);

// Get user statistics
router.get('/stats/overview',
  authenticate,
  requireAdmin,
  asyncHandler(async (req: any, res: any) => {
    const [
      totalUsers,
      activeUsers,
      superAdmins,
      admins,
      editors,
      unverifiedUsers,
      suspendedUsers,
    ] = await Promise.all([
      User.countDocuments(),
      User.countDocuments({ status: 'active' }),
      User.countDocuments({ role: 'super_admin' }),
      User.countDocuments({ role: 'admin' }),
      User.countDocuments({ role: 'editor' }),
      User.countDocuments({ emailVerified: false }),
      User.countDocuments({ status: 'suspended' }),
    ]);

    res.json({
      success: true,
      data: {
        stats: {
          totalUsers,
          activeUsers,
          superAdmins,
          admins,
          editors,
          unverifiedUsers,
          suspendedUsers,
        },
      },
    });
  })
);

export default router;
