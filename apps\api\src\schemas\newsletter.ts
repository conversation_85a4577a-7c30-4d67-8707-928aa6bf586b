import { z } from 'zod';

export const newsletterSchema = z.object({
  email: z.string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .toLowerCase()
    .trim(),
  
  name: z.string()
    .max(100, 'Name must be less than 100 characters')
    .trim()
    .optional(),
  
  preferences: z.array(z.enum([
    'blog_updates',
    'project_updates',
    'tech_insights',
    'weekly_digest',
    'announcements',
    'tutorials',
    'case_studies',
    'industry_news',
  ]))
    .max(8, 'Maximum 8 preferences allowed')
    .default([]),
  
  source: z.enum(['website', 'social_media', 'referral', 'import', 'api'])
    .default('website'),
  
  tags: z.array(z.string().trim().toLowerCase())
    .max(10, 'Maximum 10 tags allowed')
    .default([]),
  
  customFields: z.record(z.string(), z.any())
    .optional(),
  
  // Honeypot field for spam protection
  honeypot: z.string()
    .max(0, 'Spam detected')
    .optional(),
  
  // Consent
  consent: z.boolean()
    .refine(val => val === true, 'You must agree to receive newsletters'),
  
  // Double opt-in
  doubleOptIn: z.boolean()
    .default(true),
});

export const newsletterUpdateSchema = z.object({
  name: z.string()
    .max(100, 'Name must be less than 100 characters')
    .trim()
    .optional(),
  
  preferences: z.array(z.enum([
    'blog_updates',
    'project_updates',
    'tech_insights',
    'weekly_digest',
    'announcements',
    'tutorials',
    'case_studies',
    'industry_news',
  ]))
    .max(8, 'Maximum 8 preferences allowed')
    .optional(),
  
  status: z.enum(['active', 'unsubscribed', 'bounced', 'pending'])
    .optional(),
  
  tags: z.array(z.string().trim().toLowerCase())
    .max(10, 'Maximum 10 tags allowed')
    .optional(),
  
  segments: z.array(z.string().trim())
    .max(5, 'Maximum 5 segments allowed')
    .optional(),
  
  customFields: z.record(z.string(), z.any())
    .optional(),
});

export const unsubscribeSchema = z.object({
  email: z.string()
    .email('Please enter a valid email address')
    .optional(),
  
  token: z.string()
    .regex(/^[0-9a-fA-F]{24}$/, 'Invalid unsubscribe token')
    .optional(),
  
  reason: z.enum([
    'too_frequent',
    'not_relevant',
    'never_signed_up',
    'technical_issues',
    'privacy_concerns',
    'other',
  ]).optional(),
  
  feedback: z.string()
    .max(500, 'Feedback must be less than 500 characters')
    .optional(),
}).refine(
  data => data.email || data.token,
  {
    message: 'Either email or unsubscribe token is required',
    path: ['email'],
  }
);

export const newsletterQuerySchema = z.object({
  page: z.string()
    .regex(/^\d+$/, 'Page must be a number')
    .transform(Number)
    .refine(val => val > 0, 'Page must be greater than 0')
    .default('1'),
  
  limit: z.string()
    .regex(/^\d+$/, 'Limit must be a number')
    .transform(Number)
    .refine(val => val > 0 && val <= 100, 'Limit must be between 1 and 100')
    .default('50'),
  
  status: z.enum(['active', 'unsubscribed', 'bounced', 'pending'])
    .optional(),
  
  preferences: z.string()
    .transform(val => val.split(',').map(pref => pref.trim()))
    .optional(),
  
  tags: z.string()
    .transform(val => val.split(',').map(tag => tag.trim().toLowerCase()))
    .optional(),
  
  segments: z.string()
    .transform(val => val.split(',').map(segment => segment.trim()))
    .optional(),
  
  search: z.string()
    .min(1, 'Search query must not be empty')
    .max(100, 'Search query must be less than 100 characters')
    .optional(),
  
  source: z.enum(['website', 'social_media', 'referral', 'import', 'api'])
    .optional(),
  
  dateFrom: z.string()
    .datetime()
    .optional(),
  
  dateTo: z.string()
    .datetime()
    .optional(),
  
  engagementMin: z.string()
    .regex(/^0(\.\d+)?|1(\.0+)?$/, 'Engagement must be between 0 and 1')
    .transform(Number)
    .optional(),
  
  sort: z.enum(['newest', 'oldest', 'engagement', 'alphabetical'])
    .default('newest'),
});

export const bulkActionSchema = z.object({
  action: z.enum(['delete', 'unsubscribe', 'reactivate', 'add_tag', 'remove_tag', 'add_segment', 'remove_segment']),
  
  subscribers: z.array(z.string().regex(/^[0-9a-fA-F]{24}$/, 'Invalid subscriber ID'))
    .min(1, 'At least one subscriber must be selected')
    .max(1000, 'Maximum 1000 subscribers can be processed at once'),
  
  value: z.string()
    .optional(), // For tag/segment operations
});

export const campaignSchema = z.object({
  name: z.string()
    .min(1, 'Campaign name is required')
    .max(100, 'Campaign name must be less than 100 characters')
    .trim(),
  
  subject: z.string()
    .min(1, 'Email subject is required')
    .max(200, 'Subject must be less than 200 characters')
    .trim(),
  
  content: z.string()
    .min(1, 'Email content is required'),
  
  recipients: z.object({
    type: z.enum(['all', 'segments', 'tags', 'custom']),
    segments: z.array(z.string()).optional(),
    tags: z.array(z.string()).optional(),
    emails: z.array(z.string().email()).optional(),
  }),
  
  schedule: z.object({
    type: z.enum(['immediate', 'scheduled']),
    sendAt: z.string().datetime().optional(),
    timezone: z.string().optional(),
  }),
  
  settings: z.object({
    trackOpens: z.boolean().default(true),
    trackClicks: z.boolean().default(true),
    unsubscribeLink: z.boolean().default(true),
    replyTo: z.string().email().optional(),
  }).default({}),
});

export type NewsletterInput = z.infer<typeof newsletterSchema>;
export type NewsletterUpdateInput = z.infer<typeof newsletterUpdateSchema>;
export type UnsubscribeInput = z.infer<typeof unsubscribeSchema>;
export type NewsletterQuery = z.infer<typeof newsletterQuerySchema>;
export type BulkActionInput = z.infer<typeof bulkActionSchema>;
export type CampaignInput = z.infer<typeof campaignSchema>;
