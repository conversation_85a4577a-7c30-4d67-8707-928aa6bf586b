const mongoose = require('mongoose');
const fetch = require('node-fetch');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/rxy-dev';
const API_BASE_URL = 'http://localhost:3002';

async function testEmailVerification() {
  try {
    console.log('🔧 Testing Email Verification Flow...');

    // Connect to database
    console.log('🔌 Connecting to database...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to database');

    // Import the User model
    const { User } = require('@rxy/database');
    const user = await User.findOne({ email: '<EMAIL>' });

    if (!user) {
      console.error('❌ Super Admin user not found. Please run registration test first.');
      return;
    }

    console.log('👤 Found Super Admin user:', {
      id: user._id,
      email: user.email,
      name: user.name,
      role: user.role,
      emailVerified: user.emailVerified,
      hasVerificationToken: !!user.emailVerificationToken,
    });

    if (!user.emailVerificationToken) {
      console.error('❌ No email verification token found. Email might not have been sent.');
      return;
    }

    // Test email verification endpoint
    console.log('\n🔧 Testing email verification endpoint...');
    const verificationResponse = await fetch(`${API_BASE_URL}/api/auth/verify-email`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        token: user.emailVerificationToken,
      }),
    });

    const verificationData = await verificationResponse.json();
    console.log('📊 Verification response:', verificationData);

    if (verificationResponse.ok) {
      console.log('✅ Email verification successful!');

      // Test login after verification
      console.log('\n🔧 Testing login after email verification...');
      const loginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'SuperAdmin123!@#',
        }),
      });

      const loginData = await loginResponse.json();

      if (loginResponse.ok) {
        console.log('✅ Login successful after email verification!');
        console.log('👤 User data:', {
          id: loginData.data.user.id,
          email: loginData.data.user.email,
          name: loginData.data.user.name,
          role: loginData.data.user.role,
          isSuperAdmin: loginData.data.user.isSuperAdmin,
          emailVerified: loginData.data.user.emailVerified,
        });
        console.log('🔑 Access token received:', loginData.data.tokens.accessToken ? 'Yes' : 'No');

        // Test admin endpoint access
        console.log('\n🔧 Testing admin endpoint access...');
        const adminResponse = await fetch(`${API_BASE_URL}/api/users/stats/overview`, {
          headers: {
            Authorization: `Bearer ${loginData.data.tokens.accessToken}`,
          },
        });

        const adminData = await adminResponse.json();

        if (adminResponse.ok) {
          console.log('✅ Admin endpoint access successful!');
          console.log('📊 User stats:', adminData.data.stats);
        } else {
          console.log('❌ Admin endpoint access failed:', adminData.error);
        }
      } else {
        console.log('❌ Login failed after verification:', loginData.error);
      }
    } else {
      console.log('❌ Email verification failed:', verificationData.error);
    }
  } catch (error) {
    console.error('❌ Error testing email verification:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// Test resend verification functionality
async function testResendVerification() {
  try {
    console.log('\n🔧 Testing resend verification email...');

    const response = await fetch(`${API_BASE_URL}/api/auth/resend-verification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
      }),
    });

    const data = await response.json();
    console.log('📊 Resend verification response:', data);

    if (response.ok) {
      console.log('✅ Resend verification email successful!');
    } else {
      console.log('❌ Resend verification failed:', data.error);
    }
  } catch (error) {
    console.error('❌ Error testing resend verification:', error.message);
  }
}

// Run tests
async function runTests() {
  console.log('🚀 Starting Email Verification Tests\n');

  await testEmailVerification();
  await testResendVerification();

  console.log('\n🎉 Email verification tests completed!');
}

runTests();
