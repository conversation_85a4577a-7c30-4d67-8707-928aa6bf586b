import mongoose, { Schema, Document, Model } from 'mongoose';
import { nanoid } from 'nanoid';

export interface IPermissionTemplate extends Document {
  _id: string;
  name: string;
  description: string;
  resource: string;
  actions: string[];
  level: number; // 1 = Super Admin, 2 = Admin, 3 = Editor, etc.
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface IPermissionTemplateModel extends Model<IPermissionTemplate> {
  getDefaultPermissions(role: string): Promise<IPermissionTemplate[]>;
  getAllResources(): Promise<string[]>;
}

const permissionTemplateSchema = new Schema<IPermissionTemplate>(
  {
    _id: {
      type: String,
      default: () => nanoid(12),
    },
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
      trim: true,
    },
    resource: {
      type: String,
      required: true,
      enum: [
        'users',
        'blog',
        'projects',
        'messages',
        'analytics',
        'settings',
        'media',
        'comments',
        'newsletter',
        'ai',
      ],
    },
    actions: [{
      type: String,
      enum: [
        'read',
        'write',
        'delete',
        'publish',
        'moderate',
        'manage',
        'export',
        'import',
      ],
    }],
    level: {
      type: Number,
      required: true,
      min: 1,
      max: 10,
    },
    isDefault: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes
permissionTemplateSchema.index({ resource: 1, level: 1 });
permissionTemplateSchema.index({ isDefault: 1 });

// Static methods
permissionTemplateSchema.statics.getDefaultPermissions = function (role: string) {
  const levelMap: Record<string, number> = {
    'super_admin': 1,
    'admin': 2,
    'editor': 3,
    'user': 4,
  };
  
  const level = levelMap[role] || 4;
  return this.find({ level: { $gte: level }, isDefault: true });
};

permissionTemplateSchema.statics.getAllResources = function () {
  return this.distinct('resource');
};

export const PermissionTemplate: IPermissionTemplateModel = mongoose.model<IPermissionTemplate, IPermissionTemplateModel>('PermissionTemplate', permissionTemplateSchema);

// Default permission templates
export const defaultPermissions = [
  // Super Admin permissions
  {
    name: 'Super Admin - Full Access',
    description: 'Complete access to all system resources',
    resource: 'users',
    actions: ['read', 'write', 'delete', 'manage'],
    level: 1,
    isDefault: true,
  },
  {
    name: 'Super Admin - Blog Management',
    description: 'Full blog management capabilities',
    resource: 'blog',
    actions: ['read', 'write', 'delete', 'publish', 'moderate'],
    level: 1,
    isDefault: true,
  },
  {
    name: 'Super Admin - Project Management',
    description: 'Full project management capabilities',
    resource: 'projects',
    actions: ['read', 'write', 'delete', 'publish'],
    level: 1,
    isDefault: true,
  },
  
  // Admin permissions
  {
    name: 'Admin - Blog Management',
    description: 'Blog management with publishing rights',
    resource: 'blog',
    actions: ['read', 'write', 'publish', 'moderate'],
    level: 2,
    isDefault: true,
  },
  {
    name: 'Admin - Message Management',
    description: 'Message and contact form management',
    resource: 'messages',
    actions: ['read', 'write', 'moderate'],
    level: 2,
    isDefault: true,
  },
  {
    name: 'Admin - Analytics Access',
    description: 'View analytics and reports',
    resource: 'analytics',
    actions: ['read', 'export'],
    level: 2,
    isDefault: true,
  },
  
  // Editor permissions
  {
    name: 'Editor - Blog Writing',
    description: 'Create and edit blog posts',
    resource: 'blog',
    actions: ['read', 'write'],
    level: 3,
    isDefault: true,
  },
  {
    name: 'Editor - Comment Moderation',
    description: 'Moderate blog comments',
    resource: 'comments',
    actions: ['read', 'moderate'],
    level: 3,
    isDefault: true,
  },
];
