import { Router } from 'express';
import { Newsletter } from '@/models/Newsletter';
import { validateRequest } from '@/middleware/validation';
import { newsletterSchema } from '@/schemas/newsletter';
import { ApiResponse } from '@/types/api';
import { sendEmail } from '@/services/email';
import { auth } from '@/middleware/auth';

const router = Router();

// POST /api/newsletter/subscribe - Subscribe to newsletter
router.post('/subscribe', validateRequest(newsletterSchema), async (req, res) => {
  try {
    const { email, name, preferences = [] } = req.body;

    // Check if email already exists
    const existingSubscriber = await Newsletter.findOne({ email });
    
    if (existingSubscriber) {
      if (existingSubscriber.status === 'active') {
        return res.status(400).json({
          success: false,
          error: 'Email is already subscribed to the newsletter',
        });
      } else {
        // Reactivate subscription
        existingSubscriber.status = 'active';
        existingSubscriber.preferences = preferences;
        existingSubscriber.subscribedAt = new Date();
        await existingSubscriber.save();

        const response: ApiResponse<any> = {
          success: true,
          data: {
            id: existingSubscriber._id,
            email: existingSubscriber.email,
            status: 'reactivated',
          },
          message: 'Newsletter subscription reactivated successfully!',
        };

        return res.json(response);
      }
    }

    // Create new subscription
    const subscriber = new Newsletter({
      email,
      name,
      preferences,
      status: 'active',
      source: 'website',
      metadata: {
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        subscribedAt: new Date(),
      },
    });

    await subscriber.save();

    // Send welcome email
    try {
      await sendEmail({
        to: email,
        subject: 'Welcome to RxY.dev Newsletter!',
        template: 'newsletter-welcome',
        data: {
          name: name || 'Subscriber',
          preferences,
          unsubscribeUrl: `${process.env.CLIENT_URL}/newsletter/unsubscribe?token=${subscriber._id}`,
        },
      });
    } catch (emailError) {
      console.warn('Failed to send welcome email:', emailError);
    }

    const response: ApiResponse<any> = {
      success: true,
      data: {
        id: subscriber._id,
        email: subscriber.email,
        status: 'subscribed',
      },
      message: 'Successfully subscribed to the newsletter!',
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Error subscribing to newsletter:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to subscribe to newsletter. Please try again later.',
    });
  }
});

// POST /api/newsletter/unsubscribe - Unsubscribe from newsletter
router.post('/unsubscribe', async (req, res) => {
  try {
    const { email, token } = req.body;

    let subscriber;
    
    if (token) {
      subscriber = await Newsletter.findById(token);
    } else if (email) {
      subscriber = await Newsletter.findOne({ email });
    } else {
      return res.status(400).json({
        success: false,
        error: 'Email or unsubscribe token is required',
      });
    }

    if (!subscriber) {
      return res.status(404).json({
        success: false,
        error: 'Subscription not found',
      });
    }

    subscriber.status = 'unsubscribed';
    subscriber.unsubscribedAt = new Date();
    await subscriber.save();

    const response: ApiResponse<any> = {
      success: true,
      data: {
        email: subscriber.email,
        status: 'unsubscribed',
      },
      message: 'Successfully unsubscribed from the newsletter',
    };

    res.json(response);
  } catch (error) {
    console.error('Error unsubscribing from newsletter:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to unsubscribe. Please try again later.',
    });
  }
});

// GET /api/newsletter/preferences - Get newsletter preference options
router.get('/preferences', (req, res) => {
  const preferences = [
    {
      value: 'blog_updates',
      label: 'Blog Updates',
      description: 'Get notified when new blog posts are published',
    },
    {
      value: 'project_updates',
      label: 'Project Updates',
      description: 'Updates on new projects and case studies',
    },
    {
      value: 'tech_insights',
      label: 'Tech Insights',
      description: 'Technical articles and industry insights',
    },
    {
      value: 'weekly_digest',
      label: 'Weekly Digest',
      description: 'Weekly summary of content and updates',
    },
    {
      value: 'announcements',
      label: 'Announcements',
      description: 'Important announcements and news',
    },
  ];

  const response: ApiResponse<any[]> = {
    success: true,
    data: preferences,
  };

  res.json(response);
});

// GET /api/newsletter/stats - Get newsletter statistics (admin only)
router.get('/stats', auth, async (req, res) => {
  try {
    const [
      totalSubscribers,
      activeSubscribers,
      unsubscribedCount,
      recentSubscribers,
    ] = await Promise.all([
      Newsletter.countDocuments(),
      Newsletter.countDocuments({ status: 'active' }),
      Newsletter.countDocuments({ status: 'unsubscribed' }),
      Newsletter.countDocuments({
        subscribedAt: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
        status: 'active',
      }),
    ]);

    // Get preference distribution
    const preferenceStats = await Newsletter.aggregate([
      { $match: { status: 'active' } },
      { $unwind: '$preferences' },
      { $group: { _id: '$preferences', count: { $sum: 1 } } },
      { $sort: { count: -1 } },
    ]);

    const response: ApiResponse<any> = {
      success: true,
      data: {
        total: totalSubscribers,
        active: activeSubscribers,
        unsubscribed: unsubscribedCount,
        recentSubscribers,
        growthRate: totalSubscribers > 0 ? (recentSubscribers / totalSubscribers) * 100 : 0,
        preferences: preferenceStats,
      },
    };

    res.json(response);
  } catch (error) {
    console.error('Error fetching newsletter stats:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch newsletter statistics',
    });
  }
});

// GET /api/newsletter/subscribers - Get all subscribers (admin only)
router.get('/subscribers', auth, async (req, res) => {
  try {
    const { page = 1, limit = 50, status, search } = req.query;
    
    const filter: any = {};
    
    if (status) {
      filter.status = status;
    }
    
    if (search) {
      filter.$or = [
        { email: { $regex: search, $options: 'i' } },
        { name: { $regex: search, $options: 'i' } },
      ];
    }

    const skip = (Number(page) - 1) * Number(limit);
    
    const [subscribers, total] = await Promise.all([
      Newsletter.find(filter)
        .sort({ subscribedAt: -1 })
        .skip(skip)
        .limit(Number(limit))
        .lean(),
      Newsletter.countDocuments(filter),
    ]);

    const response: ApiResponse<any> = {
      success: true,
      data: {
        subscribers,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit)),
        },
      },
    };

    res.json(response);
  } catch (error) {
    console.error('Error fetching subscribers:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch subscribers',
    });
  }
});

export default router;
