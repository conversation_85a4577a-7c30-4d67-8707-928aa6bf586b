const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/rxy-dev';
const USER_EMAIL = '<EMAIL>';

async function promoteToAdmin() {
  try {
    console.log('🔌 Connecting to database...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to database');

    // Define User schema (simplified version)
    const userSchema = new mongoose.Schema({
      _id: String,
      email: String,
      name: String,
      role: String,
    });

    const User = mongoose.model('User', userSchema);

    // Find and update the user
    console.log(`🔍 Finding user with email: ${USER_EMAIL}`);
    const user = await User.findOne({ email: USER_EMAIL });

    if (!user) {
      console.error('❌ User not found');
      return;
    }

    console.log('👤 Found user:', {
      id: user._id,
      email: user.email,
      name: user.name,
      currentRole: user.role,
    });

    // Update role to admin
    console.log('🔧 Promoting user to admin...');
    await User.updateOne(
      { email: USER_EMAIL },
      { $set: { role: 'admin' } }
    );

    console.log('✅ User promoted to admin successfully!');
    
    // Verify the update
    const updatedUser = await User.findOne({ email: USER_EMAIL });
    console.log('✅ Verification - User role is now:', updatedUser.role);

  } catch (error) {
    console.error('❌ Error promoting user to admin:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// Run the script
promoteToAdmin();
