'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  MdSearch as Search,
  Md<PERSON>ilter<PERSON>ist as Filter,
  MdMail as Mail,
  MdMarkEmailRead as MailOpen,
  MdReply as Reply,
  MdArchive as Archive,
  MdDelete as Trash2,
  MdLabel as Tag,
  MdAccessTime as Clock,
  <PERSON>d<PERSON><PERSON> as User,
  <PERSON>d<PERSON><PERSON><PERSON> as AlertCircle,
  MdCheckCircle as CheckCircle,
  MdMessage as MessageSquare,
  MdSmartToy as Bot,
} from 'react-icons/md';
import { AdminLayout } from '@/components/layout/admin-layout';
import { adminApi } from '@/lib/api';

interface Message {
  id: string;
  name: string;
  email: string;
  subject: string;
  message: string;
  status: 'unread' | 'read' | 'replied' | 'archived';
  priority: 'low' | 'medium' | 'high';
  tags: string[];
  createdAt: string;
  updatedAt: string;
  reply?: {
    message: string;
    sentAt: string;
  };
  metadata: {
    userAgent: string;
    ipAddress: string;
    referrer: string;
    source: string;
  };
}

export default function MessageManagement() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedMessage, setSelectedMessage] = useState<Message | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [stats, setStats] = useState<any>({});
  const [replyText, setReplyText] = useState('');
  const [showReplyModal, setShowReplyModal] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<any[]>([]);

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session?.user?.role !== 'admin') {
      router.push('/auth/signin');
    }
  }, [session, router]);

  useEffect(() => {
    const fetchMessages = async () => {
      try {
        setLoading(true);

        const params: any = {
          page: currentPage,
          limit: 10,
        };

        if (searchQuery) params.search = searchQuery;
        if (statusFilter !== 'all') params.status = statusFilter;
        if (priorityFilter !== 'all') params.priority = priorityFilter;

        const response = await adminApi.getMessages(params);

        if (response.success) {
          setMessages(response.data.messages || []);
          setTotalPages(response.data.pagination?.totalPages || 1);
          setStats(response.data.stats || {});
        }
      } catch (error) {
        console.error('Failed to fetch messages:', error);
      } finally {
        setLoading(false);
      }
    };

    if (session?.user?.role === 'admin') {
      fetchMessages();
    }
  }, [session, currentPage, searchQuery, statusFilter, priorityFilter]);

  const handleStatusChange = async (messageId: string, newStatus: string) => {
    try {
      await adminApi.updateMessageStatus(messageId, newStatus);
      setMessages(
        messages.map(msg =>
          msg.id === messageId
            ? { ...msg, status: newStatus as any, updatedAt: new Date().toISOString() }
            : msg
        )
      );

      if (selectedMessage?.id === messageId) {
        setSelectedMessage({ ...selectedMessage, status: newStatus as any });
      }
    } catch (error) {
      console.error('Failed to update message status:', error);
      alert('Failed to update message status. Please try again.');
    }
  };

  const handleReply = async () => {
    if (!selectedMessage || !replyText.trim()) return;

    try {
      await adminApi.replyToMessage(selectedMessage.id, replyText);

      const updatedMessage = {
        ...selectedMessage,
        status: 'replied' as any,
        reply: {
          message: replyText,
          sentAt: new Date().toISOString(),
        },
        updatedAt: new Date().toISOString(),
      };

      setMessages(messages.map(msg => (msg.id === selectedMessage.id ? updatedMessage : msg)));
      setSelectedMessage(updatedMessage);
      setReplyText('');
      setShowReplyModal(false);
    } catch (error) {
      console.error('Failed to send reply:', error);
      alert('Failed to send reply. Please try again.');
    }
  };

  const fetchAiSuggestions = async (messageId: string) => {
    try {
      const response = await adminApi.getAiSuggestions(messageId);
      if (response.success) {
        setAiSuggestions(response.data.suggestions || []);
      }
    } catch (error) {
      console.error('Failed to fetch AI suggestions:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'unread':
        return <Mail className="h-4 w-4 text-blue-500" />;
      case 'read':
        return <MailOpen className="h-4 w-4 text-gray-500" />;
      case 'replied':
        return <Reply className="h-4 w-4 text-green-500" />;
      case 'archived':
        return <Archive className="h-4 w-4 text-gray-400" />;
      default:
        return <Mail className="h-4 w-4" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    const styles: Record<string, string> = {
      high: 'bg-red-100 text-red-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-gray-100 text-gray-800',
    };

    return (
      <span
        className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
          styles[priority] || styles.low
        }`}
      >
        {priority}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (status === 'loading' || loading) {
    return (
      <AdminLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600">Loading messages...</p>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout>
      <div className="flex h-[calc(100vh-8rem)]">
        {/* Messages List */}
        <div className="w-1/3 border-r border-gray-200 flex flex-col">
          {/* Header */}
          <div className="p-6 border-b border-gray-200">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-xl font-bold text-gray-900">Messages</h1>
              <div className="flex items-center space-x-2">
                {Object.entries(stats).map(([status, count]) => (
                  <span
                    key={status}
                    className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      status === 'unread'
                        ? 'bg-blue-100 text-blue-800'
                        : status === 'replied'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {status}: {String(count)}
                  </span>
                ))}
              </div>
            </div>

            {/* Filters */}
            <div className="space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search messages..."
                  value={searchQuery}
                  onChange={e => setSearchQuery(e.target.value)}
                  className="pl-10 w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                />
              </div>

              <div className="flex space-x-2">
                <select
                  value={statusFilter}
                  onChange={e => setStatusFilter(e.target.value)}
                  className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="all">All Status</option>
                  <option value="unread">Unread</option>
                  <option value="read">Read</option>
                  <option value="replied">Replied</option>
                  <option value="archived">Archived</option>
                </select>

                <select
                  value={priorityFilter}
                  onChange={e => setPriorityFilter(e.target.value)}
                  className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                >
                  <option value="all">All Priority</option>
                  <option value="high">High</option>
                  <option value="medium">Medium</option>
                  <option value="low">Low</option>
                </select>
              </div>
            </div>
          </div>

          {/* Messages List */}
          <div className="flex-1 overflow-y-auto">
            {messages.map(message => (
              <div
                key={message.id}
                onClick={() => {
                  setSelectedMessage(message);
                  if (message.status === 'unread') {
                    handleStatusChange(message.id, 'read');
                  }
                }}
                className={`p-4 border-b border-gray-200 cursor-pointer hover:bg-gray-50 ${
                  selectedMessage?.id === message.id ? 'bg-blue-50 border-blue-200' : ''
                } ${message.status === 'unread' ? 'bg-blue-25' : ''}`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">{getStatusIcon(message.status)}</div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p
                        className={`text-sm font-medium truncate ${
                          message.status === 'unread' ? 'text-gray-900' : 'text-gray-700'
                        }`}
                      >
                        {message.name}
                      </p>
                      <div className="flex items-center space-x-1">
                        {getPriorityBadge(message.priority)}
                      </div>
                    </div>
                    <p
                      className={`text-sm truncate ${
                        message.status === 'unread' ? 'text-gray-900 font-medium' : 'text-gray-600'
                      }`}
                    >
                      {message.subject}
                    </p>
                    <p className="text-xs text-gray-500 truncate mt-1">{message.message}</p>
                    <div className="flex items-center justify-between mt-2">
                      <span className="text-xs text-gray-500">{formatDate(message.createdAt)}</span>
                      {message.tags.length > 0 && (
                        <div className="flex items-center space-x-1">
                          <Tag className="h-3 w-3 text-gray-400" />
                          <span className="text-xs text-gray-500">{message.tags.length}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="p-4 border-t border-gray-200">
              <div className="flex justify-between">
                <button
                  onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50"
                >
                  Previous
                </button>
                <span className="text-sm text-gray-500">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 text-sm border border-gray-300 rounded-md disabled:opacity-50"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>

        {/* Message Detail */}
        <div className="flex-1 flex flex-col">
          {selectedMessage ? (
            <>
              {/* Message Header */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-start justify-between">
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">{selectedMessage.subject}</h2>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-600">
                      <div className="flex items-center space-x-1">
                        <User className="h-4 w-4" />
                        <span>{selectedMessage.name}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Mail className="h-4 w-4" />
                        <span>{selectedMessage.email}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{formatDate(selectedMessage.createdAt)}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {getPriorityBadge(selectedMessage.priority)}
                    <select
                      value={selectedMessage.status}
                      onChange={e => handleStatusChange(selectedMessage.id, e.target.value)}
                      className="text-sm rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    >
                      <option value="unread">Unread</option>
                      <option value="read">Read</option>
                      <option value="replied">Replied</option>
                      <option value="archived">Archived</option>
                    </select>
                  </div>
                </div>

                {/* Tags */}
                {selectedMessage.tags.length > 0 && (
                  <div className="flex items-center space-x-2 mt-3">
                    <Tag className="h-4 w-4 text-gray-400" />
                    <div className="flex flex-wrap gap-1">
                      {selectedMessage.tags.map((tag, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                        >
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Message Content */}
              <div className="flex-1 p-6 overflow-y-auto">
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                  <p className="text-gray-900 whitespace-pre-wrap">{selectedMessage.message}</p>
                </div>

                {/* Reply */}
                {selectedMessage.reply && (
                  <div className="bg-blue-50 rounded-lg p-4 mb-6">
                    <div className="flex items-center space-x-2 mb-2">
                      <Reply className="h-4 w-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-900">Your Reply</span>
                      <span className="text-xs text-blue-600">
                        {formatDate(selectedMessage.reply.sentAt)}
                      </span>
                    </div>
                    <p className="text-blue-900 whitespace-pre-wrap">
                      {selectedMessage.reply.message}
                    </p>
                  </div>
                )}

                {/* Metadata */}
                <div className="bg-gray-50 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-gray-900 mb-2">Message Details</h3>
                  <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
                    <div>
                      <span className="font-medium">Source:</span> {selectedMessage.metadata.source}
                    </div>
                    <div>
                      <span className="font-medium">IP Address:</span>{' '}
                      {selectedMessage.metadata.ipAddress}
                    </div>
                    <div className="col-span-2">
                      <span className="font-medium">Referrer:</span>{' '}
                      {selectedMessage.metadata.referrer}
                    </div>
                    <div className="col-span-2">
                      <span className="font-medium">User Agent:</span>{' '}
                      {selectedMessage.metadata.userAgent}
                    </div>
                  </div>
                </div>
              </div>

              {/* Reply Actions */}
              {selectedMessage.status !== 'replied' && (
                <div className="p-6 border-t border-gray-200">
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={() => {
                        setShowReplyModal(true);
                        fetchAiSuggestions(selectedMessage.id);
                      }}
                      className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                    >
                      <Reply className="h-4 w-4 mr-2" />
                      Reply
                    </button>
                    <button
                      onClick={() => fetchAiSuggestions(selectedMessage.id)}
                      className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                    >
                      <Bot className="h-4 w-4 mr-2" />
                      AI Suggestions
                    </button>
                  </div>
                </div>
              )}
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center">
              <div className="text-center">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900">Select a message</h3>
                <p className="text-gray-600">Choose a message from the list to view details</p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Reply Modal */}
      {showReplyModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Reply to {selectedMessage?.name}
              </h3>

              {/* AI Suggestions */}
              {aiSuggestions.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">AI Suggestions:</h4>
                  <div className="space-y-2">
                    {aiSuggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        onClick={() => setReplyText(suggestion.message)}
                        className="w-full text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50"
                      >
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-xs font-medium text-gray-600 capitalize">
                            {suggestion.type}
                          </span>
                          <span className="text-xs text-gray-500">
                            {Math.round(suggestion.confidence * 100)}% confidence
                          </span>
                        </div>
                        <p className="text-sm text-gray-900">{suggestion.message}</p>
                      </button>
                    ))}
                  </div>
                </div>
              )}

              <textarea
                value={replyText}
                onChange={e => setReplyText(e.target.value)}
                rows={8}
                className="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                placeholder="Type your reply..."
              />

              <div className="flex items-center justify-end space-x-3 mt-4">
                <button
                  onClick={() => setShowReplyModal(false)}
                  className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={handleReply}
                  disabled={!replyText.trim()}
                  className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                >
                  Send Reply
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </AdminLayout>
  );
}
