import mongoose, { Document, Schema } from 'mongoose';

export interface IProject extends Document {
  title: string;
  slug: string;
  description: string;
  content: string;
  excerpt: string;
  category: string;
  tags: string[];
  technologies: string[];
  status: 'draft' | 'published' | 'archived';
  featured: boolean;
  images: {
    thumbnail: string;
    gallery: string[];
    hero?: string;
  };
  links: {
    live?: string;
    github?: string;
    demo?: string;
    case_study?: string;
  };
  metrics: {
    views: number;
    likes: number;
    shares: number;
  };
  seo: {
    title?: string;
    description?: string;
    keywords?: string[];
    ogImage?: string;
  };
  author: mongoose.Types.ObjectId;
  publishedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const ProjectSchema = new Schema<IProject>(
  {
    title: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    slug: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: 500,
    },
    content: {
      type: String,
      required: true,
    },
    excerpt: {
      type: String,
      required: true,
      trim: true,
      maxlength: 300,
    },
    category: {
      type: String,
      required: true,
      enum: [
        'web-development',
        'mobile-app',
        'desktop-app',
        'api-development',
        'devops',
        'data-science',
        'machine-learning',
        'blockchain',
        'game-development',
        'other',
      ],
    },
    tags: [
      {
        type: String,
        trim: true,
        lowercase: true,
      },
    ],
    technologies: [
      {
        type: String,
        trim: true,
      },
    ],
    status: {
      type: String,
      enum: ['draft', 'published', 'archived'],
      default: 'draft',
    },
    featured: {
      type: Boolean,
      default: false,
    },
    images: {
      thumbnail: {
        type: String,
        required: true,
      },
      gallery: [
        {
          type: String,
        },
      ],
      hero: String,
    },
    links: {
      live: String,
      github: String,
      demo: String,
      case_study: String,
    },
    metrics: {
      views: {
        type: Number,
        default: 0,
      },
      likes: {
        type: Number,
        default: 0,
      },
      shares: {
        type: Number,
        default: 0,
      },
    },
    seo: {
      title: String,
      description: String,
      keywords: [String],
      ogImage: String,
    },
    author: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    publishedAt: Date,
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes
ProjectSchema.index({ slug: 1 }, { unique: true });
ProjectSchema.index({ status: 1, publishedAt: -1 });
ProjectSchema.index({ category: 1, status: 1 });
ProjectSchema.index({ featured: 1, status: 1 });
ProjectSchema.index({ tags: 1 });
ProjectSchema.index({ 'metrics.views': -1 });

// Virtual for URL
ProjectSchema.virtual('url').get(function () {
  return `/projects/${this.slug}`;
});

// Pre-save middleware
ProjectSchema.pre('save', function (next) {
  if (this.isModified('status') && this.status === 'published' && !this.publishedAt) {
    this.publishedAt = new Date();
  }
  next();
});

// Static methods
ProjectSchema.statics.findPublished = function () {
  return this.find({ status: 'published' }).sort({ publishedAt: -1 });
};

ProjectSchema.statics.findFeatured = function () {
  return this.find({ status: 'published', featured: true }).sort({ publishedAt: -1 });
};

ProjectSchema.statics.findByCategory = function (category: string) {
  return this.find({ status: 'published', category }).sort({ publishedAt: -1 });
};

export const Project = mongoose.model<IProject>('Project', ProjectSchema);
