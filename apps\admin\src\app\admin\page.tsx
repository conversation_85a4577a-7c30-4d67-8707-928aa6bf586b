'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  MdBar<PERSON>hart as BarChart3,
  MdArticle as FileText,
  MdMessage as MessageSquare,
  MdPeople as Users,
  MdTrendingUp as TrendingUp,
  MdVisibility as Eye,
  MdFavorite as Heart,
  MdMail as Mail,
} from 'react-icons/md';

import { adminApi } from '@/lib/api';

interface DashboardStats {
  totalPosts: number;
  totalViews: number;
  totalLikes: number;
  totalMessages: number;
  totalSubscribers: number;
  recentPosts: any[];
  recentMessages: any[];
  analytics: {
    pageViews: number;
    uniqueVisitors: number;
    bounceRate: number;
    avgSessionDuration: number;
  };
}

export default function AdminDashboard() {
  const { data: session, status } = useSession();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session?.user?.role && !['super_admin', 'admin', 'editor'].includes(session.user.role)) {
      router.push('/auth/signin');
    }
  }, [session, router]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch dashboard statistics
        const [postsResponse, messagesResponse, analyticsResponse] = await Promise.all([
          adminApi.getBlogPosts({ limit: 5 }),
          adminApi.getMessages({ limit: 5 }),
          adminApi.getAnalytics(),
        ]);

        // Mock data for demonstration - replace with actual API responses
        setStats({
          totalPosts: 24,
          totalViews: 45678,
          totalLikes: 1234,
          totalMessages: 89,
          totalSubscribers: 567,
          recentPosts: [
            {
              id: '1',
              title: 'Building Scalable React Applications',
              status: 'published',
              views: 1250,
              publishedAt: '2024-01-15',
            },
            {
              id: '2',
              title: 'The Future of AI in Web Development',
              status: 'published',
              views: 890,
              publishedAt: '2024-01-10',
            },
            {
              id: '3',
              title: 'Microservices Architecture Guide',
              status: 'draft',
              views: 0,
              publishedAt: null,
            },
          ],
          recentMessages: [
            {
              id: '1',
              name: 'John Doe',
              email: '<EMAIL>',
              subject: 'Project Inquiry',
              status: 'unread',
              createdAt: '2024-01-16',
            },
            {
              id: '2',
              name: 'Jane Smith',
              email: '<EMAIL>',
              subject: 'Collaboration Opportunity',
              status: 'read',
              createdAt: '2024-01-15',
            },
          ],
          analytics: {
            pageViews: 12345,
            uniqueVisitors: 8901,
            bounceRate: 35.2,
            avgSessionDuration: 245,
          },
        });
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (session?.user?.role && ['super_admin', 'admin', 'editor'].includes(session.user.role)) {
      fetchDashboardData();
    }
  }, [session]);

  if (status === 'loading' || loading) {
    return (
      <div className="flex items-center justify-center min-h-[60vh]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Failed to load dashboard data.</p>
      </div>
    );
  }

  const statCards = [
    {
      name: 'Total Posts',
      value: stats.totalPosts,
      icon: FileText,
      color: 'bg-blue-500',
      change: '+12%',
    },
    {
      name: 'Total Views',
      value: stats.totalViews.toLocaleString(),
      icon: Eye,
      color: 'bg-green-500',
      change: '+18%',
    },
    {
      name: 'Total Likes',
      value: stats.totalLikes.toLocaleString(),
      icon: Heart,
      color: 'bg-red-500',
      change: '+8%',
    },
    {
      name: 'Messages',
      value: stats.totalMessages,
      icon: MessageSquare,
      color: 'bg-yellow-500',
      change: '+5%',
    },
    {
      name: 'Subscribers',
      value: stats.totalSubscribers,
      icon: Mail,
      color: 'bg-purple-500',
      change: '+15%',
    },
  ];

  return (
    <div className="space-y-6">
        {/* Header */}
        <div className="animate-fade-in">
          <h1 className="text-3xl font-bold" style={{ color: 'var(--color-gray-900)' }}>
            Dashboard
          </h1>
          <p className="text-lg mt-2" style={{ color: 'var(--color-gray-600)' }}>
            Welcome back, <span className="font-medium">{session?.user?.name}</span>
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 animate-slide-up">
          {statCards.map((stat, index) => (
            <div
              key={stat.name}
              className="card p-6 hover:shadow-lg transition-all duration-300"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="flex items-center">
                <div
                  className={`${stat.color} rounded-xl p-3 shadow-md`}
                  style={{
                    background: `linear-gradient(135deg, ${stat.color.replace('bg-', 'var(--color-')}-500), ${stat.color.replace('bg-', 'var(--color-')}-600))`
                  }}
                >
                  <stat.icon className="h-6 w-6 text-white" />
                </div>
                <div className="ml-4 flex-1">
                  <p className="text-sm font-medium" style={{ color: 'var(--color-gray-600)' }}>
                    {stat.name}
                  </p>
                  <p className="text-2xl font-bold" style={{ color: 'var(--color-gray-900)' }}>
                    {stat.value}
                  </p>
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <TrendingUp className="h-4 w-4 mr-1" style={{ color: 'var(--color-success-600)' }} />
                <span className="text-sm font-medium" style={{ color: 'var(--color-success-600)' }}>
                  {stat.change}
                </span>
                <span className="text-sm ml-1" style={{ color: 'var(--color-gray-500)' }}>
                  from last month
                </span>
              </div>
            </div>
          ))}
        </div>

        {/* Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Posts */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Recent Posts</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {stats.recentPosts.map(post => (
                  <div key={post.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{post.title}</p>
                      <p className="text-sm text-gray-500">
                        {post.status === 'published' ? `${post.views} views` : 'Draft'}
                      </p>
                    </div>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        post.status === 'published'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }`}
                    >
                      {post.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Recent Messages */}
          <div className="bg-white rounded-lg shadow">
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Recent Messages</h2>
            </div>
            <div className="p-6">
              <div className="space-y-4">
                {stats.recentMessages.map(message => (
                  <div key={message.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{message.subject}</p>
                      <p className="text-sm text-gray-500">from {message.name}</p>
                    </div>
                    <span
                      className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        message.status === 'unread'
                          ? 'bg-blue-100 text-blue-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {message.status}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Analytics Overview */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-lg font-medium text-gray-900">Analytics Overview</h2>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">
                  {stats.analytics.pageViews.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Page Views</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">
                  {stats.analytics.uniqueVisitors.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Unique Visitors</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">{stats.analytics.bounceRate}%</p>
                <p className="text-sm text-gray-600">Bounce Rate</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-gray-900">
                  {Math.floor(stats.analytics.avgSessionDuration / 60)}m{' '}
                  {stats.analytics.avgSessionDuration % 60}s
                </p>
                <p className="text-sm text-gray-600">Avg. Session</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
