'use client';

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import {
  MdPeople as Users,
  MdArticle as FileText,
  MdMessage as MessageSquare,
  MdBarChart as BarChart3,
  MdTrendingUp as TrendingUp,
  MdVisibility as Eye,
  MdFavorite as Heart,
  MdEmail as Mail,
} from 'react-icons/md';
import {
  DashboardStatsLoading,
  DashboardContentLoading,
  DashboardAnalyticsLoading,
  ShimmerEffect,
} from '@/components/ui/LoadingSkeleton';

import { adminApi } from '@/lib/api';

interface DashboardStats {
  totalPosts: number;
  totalViews: number;
  totalLikes: number;
  totalMessages: number;
  totalSubscribers: number;
  recentPosts: any[];
  recentMessages: any[];
  analytics: {
    pageViews: number;
    uniqueVisitors: number;
    bounceRate: number;
    avgSessionDuration: number;
  };
}

export default function AdminDashboard() {
  const { data: session, status } = useSession();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin');
    }
  }, [status, router]);

  useEffect(() => {
    if (session?.user?.role && !['super_admin', 'admin', 'editor'].includes(session.user.role)) {
      router.push('/auth/signin');
    }
  }, [session, router]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch dashboard statistics
        const [postsResponse, messagesResponse, analyticsResponse] = await Promise.all([
          adminApi.getBlogPosts({ limit: 5 }),
          adminApi.getMessages({ limit: 5 }),
          adminApi.getAnalytics(),
        ]);

        // Mock data for demonstration - replace with actual API responses
        setStats({
          totalPosts: 24,
          totalViews: 45678,
          totalLikes: 1234,
          totalMessages: 89,
          totalSubscribers: 567,
          recentPosts: [
            {
              id: '1',
              title: 'Building Scalable React Applications',
              status: 'published',
              views: 1250,
              publishedAt: '2024-01-15',
            },
            {
              id: '2',
              title: 'The Future of AI in Web Development',
              status: 'published',
              views: 890,
              publishedAt: '2024-01-10',
            },
            {
              id: '3',
              title: 'Microservices Architecture Guide',
              status: 'draft',
              views: 0,
              publishedAt: null,
            },
          ],
          recentMessages: [
            {
              id: '1',
              name: 'John Doe',
              email: '<EMAIL>',
              subject: 'Project Inquiry',
              status: 'unread',
              createdAt: '2024-01-16',
            },
            {
              id: '2',
              name: 'Jane Smith',
              email: '<EMAIL>',
              subject: 'Collaboration Opportunity',
              status: 'read',
              createdAt: '2024-01-15',
            },
          ],
          analytics: {
            pageViews: 12345,
            uniqueVisitors: 8901,
            bounceRate: 35.2,
            avgSessionDuration: 245,
          },
        });
      } catch (error) {
        console.error('Failed to fetch dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (session?.user?.role && ['super_admin', 'admin', 'editor'].includes(session.user.role)) {
      fetchDashboardData();
    }
  }, [session]);

  if (status === 'loading' || loading) {
    return (
      <div className="space-y-8 p-6">
        <ShimmerEffect />

        {/* Header Loading */}
        <div className="mb-8 animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-64 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-48"></div>
        </div>

        {/* Stats Loading */}
        <DashboardStatsLoading />

        {/* Content Loading */}
        <DashboardContentLoading />

        {/* Analytics Loading */}
        <DashboardAnalyticsLoading />
      </div>
    );
  }

  if (!stats) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-600">Failed to load dashboard data.</p>
      </div>
    );
  }

  const statCards = [
    {
      name: 'Total Posts',
      value: stats.totalPosts,
      icon: FileText,
      color: 'bg-blue-500',
      change: '+12%',
    },
    {
      name: 'Total Views',
      value: stats.totalViews.toLocaleString(),
      icon: Eye,
      color: 'bg-green-500',
      change: '+18%',
    },
    {
      name: 'Total Likes',
      value: stats.totalLikes.toLocaleString(),
      icon: Heart,
      color: 'bg-red-500',
      change: '+8%',
    },
    {
      name: 'Messages',
      value: stats.totalMessages,
      icon: MessageSquare,
      color: 'bg-yellow-500',
      change: '+5%',
    },
    {
      name: 'Subscribers',
      value: stats.totalSubscribers,
      icon: Mail,
      color: 'bg-purple-500',
      change: '+15%',
    },
  ];

  return (
    <div className="p-8">
      {/* TAILWIND TEST - UPDATED */}
      <div className="bg-red-500 text-white p-4 rounded-lg mb-4">
        🔥 TAILWIND TEST: If you see red background, Tailwind is working! (Updated)
      </div>

      <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
        🧪 Another test with blue styling - Updated
      </div>

      {/* Header */}
      <div className="animate-fade-in">
        <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
        <p className="text-lg mt-2 text-gray-600">
          Welcome back, <span className="font-medium">{session?.user?.name}</span>!
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 animate-slide-up">
        {statCards.map((stat, index) => (
          <div
            key={stat.name}
            className="group relative overflow-hidden rounded-2xl bg-white border border-gray-100 p-6 shadow-sm hover:shadow-xl hover:border-gray-200 transition-all duration-500 transform hover:-translate-y-1"
            style={{ animationDelay: `${index * 100}ms` }}
          >
            {/* Background Gradient */}
            <div className="absolute inset-0 bg-gradient-to-br from-transparent via-transparent to-gray-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

            {/* Content */}
            <div className="relative z-10">
              <div className="flex items-center justify-between mb-4">
                <div
                  className={`rounded-2xl p-3 shadow-lg ring-1 ring-black/5 group-hover:scale-110 transition-transform duration-300 ${
                    stat.color === 'bg-blue-500'
                      ? 'bg-gradient-to-br from-blue-500 to-blue-600'
                      : stat.color === 'bg-green-500'
                      ? 'bg-gradient-to-br from-green-500 to-green-600'
                      : stat.color === 'bg-red-500'
                      ? 'bg-gradient-to-br from-red-500 to-red-600'
                      : stat.color === 'bg-yellow-500'
                      ? 'bg-gradient-to-br from-yellow-500 to-yellow-600'
                      : 'bg-gradient-to-br from-purple-500 to-purple-600'
                  }`}
                >
                  <stat.icon className="h-6 w-6 text-white drop-shadow-sm" />
                </div>

                {/* Trend Indicator */}
                <div className="flex items-center space-x-1 px-2 py-1 rounded-full bg-green-50 border border-green-200">
                  <TrendingUp className="h-3 w-3 text-green-600" />
                  <span className="text-xs font-semibold text-green-700">{stat.change}</span>
                </div>
              </div>

              <div className="space-y-1">
                <p className="text-sm font-medium text-gray-600 tracking-wide uppercase">
                  {stat.name}
                </p>
                <p className="text-3xl font-bold text-gray-900 tracking-tight">{stat.value}</p>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-100">
                <p className="text-xs text-gray-500 flex items-center">
                  <span className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></span>
                  vs last month
                </p>
              </div>
            </div>

            {/* Hover Effect Overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />
          </div>
        ))}
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Posts */}
        <div className="group bg-white rounded-2xl border border-gray-100 shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <FileText className="h-5 w-5 mr-2 text-blue-600" />
                Recent Posts
              </h2>
              <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">
                {stats.recentPosts.length} posts
              </span>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {stats.recentPosts.map((post, index) => (
                <div
                  key={post.id}
                  className="group/item flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 transition-all duration-200 border border-transparent hover:border-gray-200"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-gray-900 truncate group-hover/item:text-blue-600 transition-colors">
                      {post.title}
                    </p>
                    <div className="flex items-center mt-1 space-x-2">
                      <p className="text-xs text-gray-500">
                        {post.status === 'published' ? `${post.views} views` : 'Draft'}
                      </p>
                      {post.publishedAt && (
                        <>
                          <span className="text-gray-300">•</span>
                          <p className="text-xs text-gray-500">{post.publishedAt}</p>
                        </>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <span
                      className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ring-1 ring-inset ${
                        post.status === 'published'
                          ? 'bg-green-50 text-green-700 ring-green-600/20'
                          : 'bg-yellow-50 text-yellow-700 ring-yellow-600/20'
                      }`}
                    >
                      {post.status}
                    </span>
                    <Eye className="h-4 w-4 text-gray-400 group-hover/item:text-gray-600 transition-colors" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Recent Messages */}
        <div className="group bg-white rounded-2xl border border-gray-100 shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center">
                <MessageSquare className="h-5 w-5 mr-2 text-green-600" />
                Recent Messages
              </h2>
              <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">
                {stats.recentMessages.length} messages
              </span>
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {stats.recentMessages.map((message, index) => (
                <div
                  key={message.id}
                  className="group/item flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 transition-all duration-200 border border-transparent hover:border-gray-200"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-semibold text-gray-900 truncate group-hover/item:text-green-600 transition-colors">
                      {message.subject}
                    </p>
                    <div className="flex items-center mt-1 space-x-2">
                      <p className="text-xs text-gray-500">from {message.name}</p>
                      <span className="text-gray-300">•</span>
                      <p className="text-xs text-gray-500">{message.createdAt}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <span
                      className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ring-1 ring-inset ${
                        message.status === 'unread'
                          ? 'bg-blue-50 text-blue-700 ring-blue-600/20'
                          : 'bg-gray-50 text-gray-700 ring-gray-600/20'
                      }`}
                    >
                      {message.status}
                    </span>
                    <Mail className="h-4 w-4 text-gray-400 group-hover/item:text-gray-600 transition-colors" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Analytics Overview */}
      <div className="bg-white rounded-2xl border border-gray-100 shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden">
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-100">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
              Analytics Overview
            </h2>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">
                Last 30 days
              </span>
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </div>
        </div>
        <div className="p-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {[
              {
                label: 'Page Views',
                value: stats.analytics.pageViews.toLocaleString(),
                icon: Eye,
                color: 'blue',
                trend: '+12.5%',
              },
              {
                label: 'Unique Visitors',
                value: stats.analytics.uniqueVisitors.toLocaleString(),
                icon: Users,
                color: 'green',
                trend: '+8.2%',
              },
              {
                label: 'Bounce Rate',
                value: `${stats.analytics.bounceRate}%`,
                icon: TrendingUp,
                color: 'yellow',
                trend: '-2.1%',
              },
              {
                label: 'Avg. Session',
                value: `${Math.floor(stats.analytics.avgSessionDuration / 60)}m ${
                  stats.analytics.avgSessionDuration % 60
                }s`,
                icon: BarChart3,
                color: 'purple',
                trend: '+15.3%',
              },
            ].map((metric, index) => (
              <div
                key={metric.label}
                className="group relative text-center p-6 rounded-xl bg-gradient-to-br from-gray-50 to-white border border-gray-100 hover:border-gray-200 hover:shadow-md transition-all duration-300"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex justify-center mb-4">
                  <div
                    className={`p-3 rounded-2xl ${
                      metric.color === 'blue'
                        ? 'bg-blue-100 text-blue-600'
                        : metric.color === 'green'
                        ? 'bg-green-100 text-green-600'
                        : metric.color === 'yellow'
                        ? 'bg-yellow-100 text-yellow-600'
                        : 'bg-purple-100 text-purple-600'
                    } group-hover:scale-110 transition-transform duration-300`}
                  >
                    <metric.icon className="h-6 w-6" />
                  </div>
                </div>

                <p className="text-3xl font-bold text-gray-900 mb-2 tracking-tight">
                  {metric.value}
                </p>
                <p className="text-sm font-medium text-gray-600 mb-3">{metric.label}</p>

                <div className="flex items-center justify-center space-x-1">
                  <span
                    className={`text-xs font-semibold ${
                      metric.trend.startsWith('+') ? 'text-green-600' : 'text-red-600'
                    }`}
                  >
                    {metric.trend}
                  </span>
                  <span className="text-xs text-gray-500">vs last month</span>
                </div>

                {/* Progress bar */}
                <div className="mt-4 w-full bg-gray-200 rounded-full h-1.5">
                  <div
                    className={`h-1.5 rounded-full transition-all duration-1000 ${
                      metric.color === 'blue'
                        ? 'bg-blue-500'
                        : metric.color === 'green'
                        ? 'bg-green-500'
                        : metric.color === 'yellow'
                        ? 'bg-yellow-500'
                        : 'bg-purple-500'
                    }`}
                    style={{
                      width: `${Math.min(100, Math.abs(parseFloat(metric.trend)) * 10)}%`,
                      animationDelay: `${index * 200}ms`,
                    }}
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
