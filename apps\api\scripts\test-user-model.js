const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/rxy-dev';

async function testUserModel() {
  try {
    console.log('🔌 Connecting to database...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to database');

    // Clear any existing models
    if (mongoose.models.User) {
      delete mongoose.models.User;
      console.log('🗑️  Cleared existing User model from cache');
    }

    // Import the User model fresh
    const { User } = require('@rxy/database');
    
    console.log('📋 Testing User model schema...');
    
    // Check the schema definition
    const schema = User.schema;
    const roleField = schema.paths.role;
    
    console.log('🔍 Role field definition:');
    console.log('   Type:', roleField.instance);
    console.log('   Enum values:', roleField.enumValues);
    console.log('   Default:', roleField.defaultValue);

    // Try to create a test user with super_admin role
    console.log('\n🧪 Testing user creation with super_admin role...');
    
    const testUser = new User({
      firstName: 'Test',
      lastName: 'SuperAdmin',
      name: 'Test SuperAdmin',
      email: '<EMAIL>',
      password: 'TestPassword123!',
      role: 'super_admin',
      isSuperAdmin: true,
      isFirstUser: true,
      status: 'active',
      permissions: [
        { resource: 'users', actions: ['read', 'write', 'delete', 'manage'] }
      ],
    });

    // Validate the user without saving
    const validationError = testUser.validateSync();
    if (validationError) {
      console.error('❌ Validation failed:', validationError.message);
      console.error('   Errors:', Object.keys(validationError.errors));
      for (const [field, error] of Object.entries(validationError.errors)) {
        console.error(`   ${field}: ${error.message}`);
      }
    } else {
      console.log('✅ User validation passed!');
      console.log('👤 Test user role:', testUser.role);
    }

  } catch (error) {
    console.error('❌ Error testing User model:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// Run the test
testUserModel();
