import mongoose, { Schema, Document, Model } from 'mongoose';
import { generateId } from '@rxy/shared';
import type { BlogCategory as BlogCategoryType } from '@rxy/shared';

export interface IBlogCategory extends Omit<BlogCategoryType, 'id'>, Document {
  _id: string;
}

const blogCategorySchema = new Schema<IBlogCategory>(
  {
    _id: {
      type: String,
      default: () => generateId(),
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    slug: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
    },
    description: {
      type: String,
      trim: true,
    },
    color: {
      type: String,
      default: '#3B82F6', // Default blue color
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes
blogCategorySchema.index({ slug: 1 }, { unique: true });
blogCategorySchema.index({ name: 1 }, { unique: true });

// Virtual for id
blogCategorySchema.virtual('id').get(function () {
  return this._id;
});

// Ensure virtual fields are serialized
blogCategorySchema.set('toJSON', {
  virtuals: true,
  transform: function (doc, ret) {
    delete ret._id;
    delete ret.__v;
    return ret;
  },
});

// Static methods
blogCategorySchema.statics.findBySlug = function (slug: string) {
  return this.findOne({ slug: slug.toLowerCase() });
};

export const BlogCategory: Model<IBlogCategory> = mongoose.model<IBlogCategory>(
  'BlogCategory',
  blogCategorySchema
);
