const fetch = require('node-fetch');

const ADMIN_BASE_URL = 'http://localhost:3003';

async function testCompleteFlow() {
  console.log('🔧 Testing Complete Browser Authentication Flow...\n');

  try {
    // Step 1: Get initial session (should be empty)
    console.log('1. Checking initial session...');
    const initialSessionResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/session`);
    const initialSession = await initialSessionResponse.json();
    console.log('📊 Initial Session:', initialSession);

    // Step 2: Get CSRF token
    console.log('\n2. Getting CSRF token...');
    const csrfResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/csrf`);
    const csrfData = await csrfResponse.json();
    console.log('📊 CSRF Token:', csrfData.csrfToken ? 'Present' : 'Missing');

    // Step 3: Perform login
    console.log('\n3. Performing login...');
    const loginData = new URLSearchParams({
      email: '<EMAIL>',
      password: 'SuperAdmin123!@#',
      csrfToken: csrfData.csrfToken,
      callbackUrl: `${ADMIN_BASE_URL}/admin`,
      json: 'true'
    });

    const loginResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': csrfResponse.headers.get('set-cookie') || '',
      },
      body: loginData.toString(),
    });

    console.log('📊 Login Status:', loginResponse.status);
    const loginResult = await loginResponse.json();
    console.log('📊 Login Result:', loginResult);

    // Extract cookies from login response
    const loginCookies = loginResponse.headers.get('set-cookie') || '';
    console.log('📊 Login Cookies Set:', loginCookies ? 'Yes' : 'No');

    // Step 4: Check session immediately after login
    console.log('\n4. Checking session immediately after login...');
    const immediateSessionResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/session`, {
      headers: {
        'Cookie': loginCookies,
      },
    });

    const immediateSession = await immediateSessionResponse.json();
    console.log('📊 Immediate Session:', immediateSession);

    // Step 5: Wait a moment and check session again
    console.log('\n5. Waiting 2 seconds and checking session again...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    const delayedSessionResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/session`, {
      headers: {
        'Cookie': loginCookies,
      },
    });

    const delayedSession = await delayedSessionResponse.json();
    console.log('📊 Delayed Session:', delayedSession);

    // Step 6: Test admin page access
    console.log('\n6. Testing admin page access...');
    const adminPageResponse = await fetch(`${ADMIN_BASE_URL}/admin`, {
      headers: {
        'Cookie': loginCookies,
      },
      redirect: 'manual', // Don't follow redirects
    });

    console.log('📊 Admin Page Status:', adminPageResponse.status);
    console.log('📊 Admin Page Headers:', Object.fromEntries(adminPageResponse.headers.entries()));

    if (adminPageResponse.status === 302 || adminPageResponse.status === 307) {
      console.log('📊 Redirect Location:', adminPageResponse.headers.get('location'));
    }

    // Step 7: Test API proxy endpoints
    console.log('\n7. Testing API proxy endpoints...');
    const statsResponse = await fetch(`${ADMIN_BASE_URL}/api/users/stats/overview`, {
      headers: {
        'Cookie': loginCookies,
      },
    });

    console.log('📊 Stats API Status:', statsResponse.status);
    if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('📊 Stats Data:', statsData);
    } else {
      const errorText = await statsResponse.text();
      console.log('📊 Stats Error:', errorText);
    }

  } catch (error) {
    console.error('❌ Error during flow test:', error.message);
  }
}

async function testSessionPersistence() {
  console.log('\n🔧 Testing Session Persistence...\n');

  try {
    // Simulate multiple session checks like a browser would do
    for (let i = 1; i <= 5; i++) {
      console.log(`${i}. Session check ${i}...`);
      const sessionResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/session`);
      const sessionData = await sessionResponse.json();
      console.log(`📊 Session ${i}:`, sessionData.user ? {
        email: sessionData.user.email,
        role: sessionData.user.role,
        id: sessionData.user.id
      } : 'No session');
      
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  } catch (error) {
    console.error('❌ Error during session persistence test:', error.message);
  }
}

async function runDiagnostics() {
  console.log('🚀 Starting Complete Authentication Flow Diagnostics\n');
  
  await testCompleteFlow();
  await testSessionPersistence();
  
  console.log('\n🎉 Diagnostics completed!');
  console.log('\n📝 Analysis:');
  console.log('1. Check if login returns success but session is empty');
  console.log('2. Look for timing issues between login and session retrieval');
  console.log('3. Verify if admin page redirects properly');
  console.log('4. Check if API proxy endpoints work with session');
}

runDiagnostics();
