import mongoose, { Schema, Document, Model } from 'mongoose';
import { nanoid } from 'nanoid';
import * as crypto from 'crypto';

export interface IComment extends Document {
  _id: string;
  postId: string; // Reference to blog post
  parentId?: string; // For nested comments
  
  // Commenter info (not admin users)
  name: string;
  email: string;
  website?: string;
  avatar?: string;
  
  // Comment content
  content: string;
  status: 'pending' | 'approved' | 'rejected' | 'spam';
  
  // Verification
  emailVerified: boolean;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  
  // Metadata
  ipAddress?: string;
  userAgent?: string;
  
  // Moderation
  moderatedBy?: string; // Admin user ID
  moderatedAt?: Date;
  moderationReason?: string;
  
  // Timestamps
  createdAt: Date;
  updatedAt: Date;
  
  // Methods
  generateEmailVerificationToken(): string;
  isVerified(): boolean;
}

interface ICommentModel extends Model<IComment> {
  findByPost(postId: string): Promise<IComment[]>;
  findPending(): Promise<IComment[]>;
  findByEmail(email: string): Promise<IComment[]>;
}

const commentSchema = new Schema<IComment>(
  {
    _id: {
      type: String,
      default: () => nanoid(12),
    },
    postId: {
      type: String,
      required: true,
      ref: 'BlogPost',
    },
    parentId: {
      type: String,
      ref: 'Comment',
      default: null,
    },
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
    },
    website: {
      type: String,
      trim: true,
      maxlength: 200,
    },
    avatar: {
      type: String,
      default: null,
    },
    content: {
      type: String,
      required: true,
      maxlength: 2000,
    },
    status: {
      type: String,
      enum: ['pending', 'approved', 'rejected', 'spam'],
      default: 'pending',
    },
    emailVerified: {
      type: Boolean,
      default: false,
    },
    emailVerificationToken: {
      type: String,
      default: null,
    },
    emailVerificationExpires: {
      type: Date,
      default: null,
    },
    ipAddress: {
      type: String,
      default: null,
    },
    userAgent: {
      type: String,
      default: null,
    },
    moderatedBy: {
      type: String,
      ref: 'User',
      default: null,
    },
    moderatedAt: {
      type: Date,
      default: null,
    },
    moderationReason: {
      type: String,
      maxlength: 500,
      default: null,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes
commentSchema.index({ postId: 1, status: 1, createdAt: -1 });
commentSchema.index({ email: 1 });
commentSchema.index({ status: 1, createdAt: -1 });
commentSchema.index({ parentId: 1 });
commentSchema.index({ emailVerificationToken: 1 });

// Virtual for nested replies
commentSchema.virtual('replies', {
  ref: 'Comment',
  localField: '_id',
  foreignField: 'parentId',
});

// Ensure virtual fields are serialized
commentSchema.set('toJSON', {
  virtuals: true,
  transform: function (_doc, ret) {
    delete ret.__v;
    delete ret.emailVerificationToken;
    delete ret.ipAddress;
    delete ret.userAgent;
    return ret;
  },
});

// Instance methods
commentSchema.methods.generateEmailVerificationToken = function (): string {
  const token = crypto.randomBytes(32).toString('hex');
  this.emailVerificationToken = token;
  this.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
  return token;
};

commentSchema.methods.isVerified = function (): boolean {
  return this.emailVerified;
};

// Static methods
commentSchema.statics.findByPost = function (postId: string) {
  return this.find({ postId, status: 'approved' })
    .sort({ createdAt: 1 })
    .populate('replies');
};

commentSchema.statics.findPending = function () {
  return this.find({ status: 'pending' }).sort({ createdAt: -1 });
};

commentSchema.statics.findByEmail = function (email: string) {
  return this.find({ email: email.toLowerCase() }).sort({ createdAt: -1 });
};

export const Comment: ICommentModel = mongoose.model<IComment, ICommentModel>('Comment', commentSchema);
