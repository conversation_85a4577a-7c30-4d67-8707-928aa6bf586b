import { Router } from 'express';
import { Message } from '@/models/Message';
import { validateRequest } from '@/middleware/validation';
import { contactSchema } from '@/schemas/contact';
import { ApiResponse } from '@/types/api';
import { sendEmail } from '@/services/email';
import { generateAIResponse } from '@/services/ai';

const router = Router();

// POST /api/contact - Submit contact form
router.post('/', validateRequest(contactSchema), async (req, res) => {
  try {
    const { name, email, subject, message, type = 'general' } = req.body;

    // Create message in database
    const newMessage = new Message({
      name,
      email,
      subject,
      message,
      type,
      status: 'unread',
      source: 'contact_form',
      metadata: {
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        timestamp: new Date(),
      },
    });

    await newMessage.save();

    // Generate AI response suggestion (optional)
    let aiSuggestion = null;
    try {
      aiSuggestion = await generateAIResponse(message, type);
    } catch (aiError) {
      console.warn('AI response generation failed:', aiError);
    }

    // Send notification email to admin (optional)
    try {
      await sendEmail({
        to: process.env.ADMIN_EMAIL || '<EMAIL>',
        subject: `New Contact Form Submission: ${subject}`,
        template: 'contact-notification',
        data: {
          name,
          email,
          subject,
          message,
          type,
          messageId: newMessage._id,
          aiSuggestion,
        },
      });
    } catch (emailError) {
      console.warn('Failed to send notification email:', emailError);
    }

    // Send auto-reply to user
    try {
      await sendEmail({
        to: email,
        subject: `Thank you for contacting RxY.dev`,
        template: 'contact-auto-reply',
        data: {
          name,
          subject,
          message,
        },
      });
    } catch (emailError) {
      console.warn('Failed to send auto-reply email:', emailError);
    }

    const response: ApiResponse<any> = {
      success: true,
      data: {
        id: newMessage._id,
        status: 'received',
        estimatedResponseTime: '24-48 hours',
      },
      message: 'Message sent successfully. We\'ll get back to you soon!',
    };

    res.status(201).json(response);
  } catch (error) {
    console.error('Error submitting contact form:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to send message. Please try again later.',
    });
  }
});

// GET /api/contact/types - Get available contact types
router.get('/types', (req, res) => {
  const types = [
    {
      value: 'general',
      label: 'General Inquiry',
      description: 'General questions or information requests',
    },
    {
      value: 'project',
      label: 'Project Collaboration',
      description: 'Discuss potential project opportunities',
    },
    {
      value: 'consulting',
      label: 'Consulting Services',
      description: 'Technical consulting and advisory services',
    },
    {
      value: 'speaking',
      label: 'Speaking Engagement',
      description: 'Conference talks, workshops, or presentations',
    },
    {
      value: 'media',
      label: 'Media & Press',
      description: 'Interview requests and media inquiries',
    },
    {
      value: 'support',
      label: 'Technical Support',
      description: 'Help with existing projects or technical issues',
    },
  ];

  const response: ApiResponse<any[]> = {
    success: true,
    data: types,
  };

  res.json(response);
});

// GET /api/contact/status/:id - Check message status
router.get('/status/:id', async (req, res) => {
  try {
    const { id } = req.params;
    
    const message = await Message.findById(id)
      .select('status createdAt updatedAt')
      .lean();

    if (!message) {
      return res.status(404).json({
        success: false,
        error: 'Message not found',
      });
    }

    const response: ApiResponse<any> = {
      success: true,
      data: {
        id,
        status: message.status,
        submittedAt: message.createdAt,
        lastUpdated: message.updatedAt,
      },
    };

    res.json(response);
  } catch (error) {
    console.error('Error checking message status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check message status',
    });
  }
});

export default router;
