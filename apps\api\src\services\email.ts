import nodemailer from 'nodemailer';
import { config } from '@/config';

interface EmailOptions {
  to: string | string[];
  subject: string;
  template?: string;
  html?: string;
  text?: string;
  data?: Record<string, any>;
  attachments?: Array<{
    filename: string;
    content?: Buffer;
    path?: string;
    contentType?: string;
  }>;
  replyTo?: string;
  cc?: string | string[];
  bcc?: string | string[];
}

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

// Email templates
const templates: Record<string, (data: any) => EmailTemplate> = {
  'contact-notification': (data) => ({
    subject: `New Contact Form Submission: ${data.subject}`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #0F172A;">New Contact Form Submission</h2>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <p><strong>Name:</strong> ${data.name}</p>
          <p><strong>Email:</strong> ${data.email}</p>
          <p><strong>Subject:</strong> ${data.subject}</p>
          <p><strong>Type:</strong> ${data.type}</p>
          <p><strong>Message:</strong></p>
          <div style="background: white; padding: 15px; border-radius: 4px; margin-top: 10px;">
            ${data.message.replace(/\n/g, '<br>')}
          </div>
        </div>
        
        ${data.aiSuggestion ? `
          <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
            <h3 style="color: #1976d2; margin-top: 0;">AI Response Suggestion</h3>
            <p>${data.aiSuggestion}</p>
          </div>
        ` : ''}
        
        <p style="margin-top: 30px;">
          <a href="${process.env.ADMIN_URL}/admin/messages/${data.messageId}" 
             style="background: #0F172A; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
            View in Admin Panel
          </a>
        </p>
      </div>
    `,
    text: `
New Contact Form Submission

Name: ${data.name}
Email: ${data.email}
Subject: ${data.subject}
Type: ${data.type}

Message:
${data.message}

${data.aiSuggestion ? `AI Suggestion: ${data.aiSuggestion}` : ''}

View in admin panel: ${process.env.ADMIN_URL}/admin/messages/${data.messageId}
    `,
  }),

  'contact-auto-reply': (data) => ({
    subject: 'Thank you for contacting RxY.dev',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #0F172A;">Thank you for reaching out!</h2>
        
        <p>Hi ${data.name},</p>
        
        <p>Thank you for contacting me through RxY.dev. I've received your message about "${data.subject}" and will get back to you within 24-48 hours.</p>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">Your Message:</h3>
          <p>${data.message.replace(/\n/g, '<br>')}</p>
        </div>
        
        <p>In the meantime, feel free to:</p>
        <ul>
          <li><a href="${process.env.CLIENT_URL}/blog">Check out my latest blog posts</a></li>
          <li><a href="${process.env.CLIENT_URL}/projects">View my recent projects</a></li>
          <li><a href="${process.env.CLIENT_URL}/newsletter">Subscribe to my newsletter</a></li>
        </ul>
        
        <p>Best regards,<br>RxY</p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="font-size: 12px; color: #666;">
          This is an automated response. Please do not reply to this email.
        </p>
      </div>
    `,
    text: `
Hi ${data.name},

Thank you for contacting me through RxY.dev. I've received your message about "${data.subject}" and will get back to you within 24-48 hours.

Your Message:
${data.message}

In the meantime, feel free to check out my blog, projects, or subscribe to my newsletter at ${process.env.CLIENT_URL}

Best regards,
RxY

---
This is an automated response. Please do not reply to this email.
    `,
  }),

  'newsletter-welcome': (data) => ({
    subject: 'Welcome to RxY.dev Newsletter!',
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #0F172A;">Welcome to RxY.dev Newsletter!</h2>
        
        <p>Hi ${data.name || 'there'},</p>
        
        <p>Thank you for subscribing to my newsletter! You'll receive updates about:</p>
        
        <ul>
          ${data.preferences.map((pref: string) => `<li>${pref.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}</li>`).join('')}
        </ul>
        
        <p>I'm excited to share my journey in software development, latest projects, and insights from the tech world with you.</p>
        
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
          <h3 style="margin-top: 0;">What to expect:</h3>
          <ul>
            <li>🚀 Latest project updates and case studies</li>
            <li>💡 Technical insights and tutorials</li>
            <li>📝 New blog posts and articles</li>
            <li>🎯 Industry trends and best practices</li>
          </ul>
        </div>
        
        <p style="margin-top: 30px;">
          <a href="${process.env.CLIENT_URL}" 
             style="background: #0F172A; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px;">
            Visit RxY.dev
          </a>
        </p>
        
        <hr style="margin: 30px 0; border: none; border-top: 1px solid #eee;">
        <p style="font-size: 12px; color: #666;">
          You can <a href="${data.unsubscribeUrl}">unsubscribe</a> at any time.
        </p>
      </div>
    `,
    text: `
Welcome to RxY.dev Newsletter!

Hi ${data.name || 'there'},

Thank you for subscribing to my newsletter! You'll receive updates about your selected preferences.

What to expect:
- Latest project updates and case studies
- Technical insights and tutorials  
- New blog posts and articles
- Industry trends and best practices

Visit RxY.dev: ${process.env.CLIENT_URL}

You can unsubscribe at any time: ${data.unsubscribeUrl}
    `,
  }),
};

class EmailService {
  private transporter: nodemailer.Transporter | null = null;

  constructor() {
    this.initializeTransporter();
  }

  private initializeTransporter() {
    try {
      if (config.NODE_ENV === 'development') {
        // Use Ethereal for development
        this.transporter = nodemailer.createTransporter({
          host: 'smtp.ethereal.email',
          port: 587,
          auth: {
            user: '<EMAIL>',
            pass: 'ethereal.pass',
          },
        });
      } else {
        // Production email configuration
        this.transporter = nodemailer.createTransporter({
          host: process.env.SMTP_HOST || 'smtp.gmail.com',
          port: parseInt(process.env.SMTP_PORT || '587'),
          secure: process.env.SMTP_SECURE === 'true',
          auth: {
            user: process.env.SMTP_USER,
            pass: process.env.SMTP_PASS,
          },
        });
      }
    } catch (error) {
      console.error('Failed to initialize email transporter:', error);
    }
  }

  async sendEmail(options: EmailOptions): Promise<boolean> {
    try {
      if (!this.transporter) {
        console.warn('Email transporter not initialized');
        return false;
      }

      let { html, text, subject } = options;

      // Use template if specified
      if (options.template && templates[options.template]) {
        const template = templates[options.template](options.data || {});
        html = template.html;
        text = template.text;
        subject = template.subject;
      }

      const mailOptions = {
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: Array.isArray(options.to) ? options.to.join(', ') : options.to,
        subject,
        html,
        text,
        replyTo: options.replyTo,
        cc: options.cc,
        bcc: options.bcc,
        attachments: options.attachments,
      };

      const result = await this.transporter.sendMail(mailOptions);
      
      if (config.NODE_ENV === 'development') {
        console.log('Email sent:', nodemailer.getTestMessageUrl(result));
      }

      return true;
    } catch (error) {
      console.error('Failed to send email:', error);
      return false;
    }
  }

  async verifyConnection(): Promise<boolean> {
    try {
      if (!this.transporter) return false;
      await this.transporter.verify();
      return true;
    } catch (error) {
      console.error('Email connection verification failed:', error);
      return false;
    }
  }
}

const emailService = new EmailService();

export const sendEmail = (options: EmailOptions) => emailService.sendEmail(options);
export const verifyEmailConnection = () => emailService.verifyConnection();
