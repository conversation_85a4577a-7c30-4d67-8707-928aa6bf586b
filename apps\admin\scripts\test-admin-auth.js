const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3002';
const ADMIN_BASE_URL = 'http://localhost:3003';

async function testAdminAuth() {
  console.log('🔧 Testing Admin Authentication Flow...\n');

  try {
    // Test 1: Direct API login
    console.log('1. Testing direct API login...');
    const apiResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'SuperAdmin123!@#',
      }),
    });

    const apiData = await apiResponse.json();
    console.log('📊 API Response Status:', apiResponse.status);
    console.log('📊 API Response:', {
      success: apiData.success,
      user: apiData.data?.user ? {
        id: apiData.data.user.id,
        email: apiData.data.user.email,
        name: apiData.data.user.name,
        role: apiData.data.user.role,
        emailVerified: apiData.data.user.emailVerified,
      } : null,
      hasTokens: !!apiData.data?.tokens,
      accessToken: apiData.data?.tokens?.accessToken ? 'Present' : 'Missing',
    });

    if (apiResponse.ok && apiData.success) {
      console.log('✅ Direct API login successful!\n');
      
      // Test 2: Test admin endpoint access
      console.log('2. Testing admin endpoint access...');
      const statsResponse = await fetch(`${API_BASE_URL}/api/users/stats/overview`, {
        headers: {
          'Authorization': `Bearer ${apiData.data.tokens.accessToken}`,
        },
      });

      const statsData = await statsResponse.json();
      console.log('📊 Stats Response Status:', statsResponse.status);
      
      if (statsResponse.ok) {
        console.log('✅ Admin endpoint access successful!');
        console.log('📊 User Stats:', statsData.data?.stats);
      } else {
        console.log('❌ Admin endpoint access failed:', statsData.error);
      }
    } else {
      console.log('❌ Direct API login failed:', apiData.error || 'Unknown error');
    }

    console.log('\n3. Testing admin app connectivity...');
    
    // Test 3: Check if admin app is accessible
    try {
      const adminResponse = await fetch(`${ADMIN_BASE_URL}/auth/signin`);
      console.log('📊 Admin App Status:', adminResponse.status);
      
      if (adminResponse.ok) {
        console.log('✅ Admin app is accessible');
      } else {
        console.log('❌ Admin app is not accessible');
      }
    } catch (error) {
      console.log('❌ Cannot connect to admin app:', error.message);
    }

    console.log('\n4. Testing NextAuth API endpoint...');
    
    // Test 4: Check NextAuth providers endpoint
    try {
      const providersResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/providers`);
      const providersData = await providersResponse.json();
      console.log('📊 NextAuth Providers Status:', providersResponse.status);
      
      if (providersResponse.ok) {
        console.log('✅ NextAuth providers endpoint working');
        console.log('📊 Available providers:', Object.keys(providersData));
      } else {
        console.log('❌ NextAuth providers endpoint failed');
      }
    } catch (error) {
      console.log('❌ Cannot connect to NextAuth providers:', error.message);
    }

  } catch (error) {
    console.error('❌ Error during authentication test:', error.message);
  }
}

// Test CORS and connectivity
async function testConnectivity() {
  console.log('\n🔧 Testing Connectivity...\n');

  try {
    // Test API server health
    console.log('1. Testing API server health...');
    const apiHealthResponse = await fetch(`${API_BASE_URL}/api/auth/registration-status`);
    const apiHealthData = await apiHealthResponse.json();
    
    console.log('📊 API Health Status:', apiHealthResponse.status);
    if (apiHealthResponse.ok) {
      console.log('✅ API server is healthy');
      console.log('📊 Registration Status:', apiHealthData.data);
    } else {
      console.log('❌ API server health check failed');
    }

    // Test admin app health
    console.log('\n2. Testing admin app health...');
    const adminHealthResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/session`);
    
    console.log('📊 Admin App Health Status:', adminHealthResponse.status);
    if (adminHealthResponse.status < 500) {
      console.log('✅ Admin app is responding');
    } else {
      console.log('❌ Admin app health check failed');
    }

  } catch (error) {
    console.error('❌ Connectivity test failed:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting Admin Authentication Tests\n');
  
  await testConnectivity();
  await testAdminAuth();
  
  console.log('\n🎉 Authentication tests completed!');
  console.log('\n📝 Next Steps:');
  console.log('1. If API login works but admin app fails, check NextAuth configuration');
  console.log('2. If both fail, check if API server is running on port 3002');
  console.log('3. If admin app is not accessible, check if it\'s running on port 3003');
  console.log('4. Try logging in through the admin interface at http://localhost:3003/auth/signin');
}

runTests();
