const crypto = require('crypto');

function generateSecureSecret(length = 64) {
  return crypto.randomBytes(length).toString('hex');
}

console.log('🔐 Generating Secure JWT Secrets...\n');

const accessSecret = generateSecureSecret(64);
const refreshSecret = generateSecureSecret(64);
const nextAuthSecret = generateSecureSecret(32);

console.log('📋 Copy these secrets to your .env files:\n');

console.log('=== FOR apps/api/.env ===');
console.log(`JWT_ACCESS_SECRET=${accessSecret}`);
console.log(`JWT_REFRESH_SECRET=${refreshSecret}`);
console.log('JWT_ACCESS_EXPIRES=15m');
console.log('JWT_REFRESH_EXPIRES=7d');

console.log('\n=== FOR apps/admin/.env.local ===');
console.log(`NEXTAUTH_SECRET=${nextAuthSecret}`);

console.log('\n🔒 Security Notes:');
console.log('1. These secrets are cryptographically secure random values');
console.log('2. Keep these secrets private and never commit them to version control');
console.log('3. Use different secrets for production environments');
console.log('4. The access secret is used for short-lived tokens (15 minutes)');
console.log('5. The refresh secret is used for long-lived tokens (7 days)');
console.log('6. The NextAuth secret is used for session encryption');

console.log('\n✅ Secrets generated successfully!');
