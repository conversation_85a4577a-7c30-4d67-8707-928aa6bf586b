import { z } from 'zod';

// Auth Schemas
export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
});

export const registerSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  email: z.string().email('Invalid email address'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
      'Password must contain uppercase, lowercase, number, and special character'
    ),
  role: z.enum(['admin', 'editor']).optional(),
  permissions: z
    .array(
      z.object({
        resource: z.string(),
        actions: z.array(z.string()),
      })
    )
    .optional(),
});

export const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
});

export const resetPasswordSchema = z
  .object({
    token: z.string().min(1, 'Reset token is required'),
    password: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        'Password must contain uppercase, lowercase, number, and special character'
      ),
    confirmPassword: z.string(),
  })
  .refine(data => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

// Blog Schemas
export const blogPostSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  slug: z
    .string()
    .min(1, 'Slug is required')
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Invalid slug format'),
  content: z.string().min(1, 'Content is required'),
  excerpt: z.string().max(500, 'Excerpt too long').optional(),
  featuredImage: z.string().url('Invalid image URL').optional(),
  tags: z.array(z.string()).default([]),
  category: z.string().min(1, 'Category is required'),
  status: z.enum(['draft', 'published', 'archived']).default('draft'),
  seo: z
    .object({
      metaTitle: z.string().max(60, 'Meta title too long').optional(),
      metaDescription: z.string().max(160, 'Meta description too long').optional(),
      keywords: z.array(z.string()).default([]),
    })
    .default({}),
  publishedAt: z.date().optional(),
});

export const blogCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(50, 'Name too long'),
  slug: z
    .string()
    .min(1, 'Slug is required')
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Invalid slug format'),
  description: z.string().max(200, 'Description too long').optional(),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, 'Invalid color format')
    .optional(),
});

// Project Schemas
export const projectSchema = z.object({
  title: z.string().min(1, 'Title is required').max(100, 'Title too long'),
  slug: z
    .string()
    .min(1, 'Slug is required')
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Invalid slug format'),
  description: z.string().min(1, 'Description is required').max(500, 'Description too long'),
  longDescription: z.string().optional(),
  featuredImage: z.string().url('Invalid image URL'),
  images: z.array(z.string().url('Invalid image URL')).default([]),
  technologies: z.array(z.string()).min(1, 'At least one technology is required'),
  category: z.string().min(1, 'Category is required'),
  status: z.enum(['active', 'completed', 'archived']).default('active'),
  githubUrl: z.string().url('Invalid GitHub URL').optional(),
  liveUrl: z.string().url('Invalid live URL').optional(),
  featured: z.boolean().default(false),
  order: z.number().int().min(0).default(0),
});

export const projectCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').max(50, 'Name too long'),
  slug: z
    .string()
    .min(1, 'Slug is required')
    .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, 'Invalid slug format'),
  description: z.string().max(200, 'Description too long').optional(),
  color: z
    .string()
    .regex(/^#[0-9A-F]{6}$/i, 'Invalid color format')
    .optional(),
});

// Contact Schema
export const contactMessageSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name too long'),
  email: z.string().email('Invalid email address'),
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject too long'),
  message: z
    .string()
    .min(10, 'Message must be at least 10 characters')
    .max(5000, 'Message too long'),
});

// Newsletter Schemas
export const newsletterSubscribeSchema = z.object({
  email: z.string().email('Invalid email address'),
  name: z.string().min(2, 'Name must be at least 2 characters').optional(),
  source: z.string().default('website'),
});

export const newsletterCampaignSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  subject: z.string().min(1, 'Subject is required').max(100, 'Subject too long'),
  content: z.string().min(1, 'Content is required'),
  status: z.enum(['draft', 'scheduled', 'sent']).default('draft'),
  scheduledAt: z.date().optional(),
});

// Settings Schemas
export const siteSettingsSchema = z.object({
  siteName: z.string().min(1, 'Site name is required').max(100, 'Site name too long'),
  siteDescription: z
    .string()
    .min(1, 'Site description is required')
    .max(500, 'Description too long'),
  siteUrl: z.string().url('Invalid site URL'),
  logo: z.string().url('Invalid logo URL').optional(),
  favicon: z.string().url('Invalid favicon URL').optional(),
  socialMedia: z
    .object({
      twitter: z.string().url('Invalid Twitter URL').optional(),
      linkedin: z.string().url('Invalid LinkedIn URL').optional(),
      github: z.string().url('Invalid GitHub URL').optional(),
      instagram: z.string().url('Invalid Instagram URL').optional(),
    })
    .default({}),
  seo: z.object({
    defaultMetaTitle: z
      .string()
      .min(1, 'Default meta title is required')
      .max(60, 'Meta title too long'),
    defaultMetaDescription: z
      .string()
      .min(1, 'Default meta description is required')
      .max(160, 'Meta description too long'),
    defaultKeywords: z.array(z.string()).default([]),
    ogImage: z.string().url('Invalid OG image URL').optional(),
  }),
  theme: z.object({
    primaryColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid primary color format'),
    accentColor: z.string().regex(/^#[0-9A-F]{6}$/i, 'Invalid accent color format'),
    darkMode: z.boolean().default(false),
  }),
  features: z
    .object({
      blog: z.boolean().default(true),
      newsletter: z.boolean().default(true),
      contact: z.boolean().default(true),
      analytics: z.boolean().default(true),
    })
    .default({}),
});

export const userProfileSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name too long'),
  email: z.string().email('Invalid email address'),
  avatar: z.string().url('Invalid avatar URL').optional(),
});

export const changePasswordSchema = z
  .object({
    currentPassword: z.string().min(1, 'Current password is required'),
    newPassword: z
      .string()
      .min(8, 'Password must be at least 8 characters')
      .regex(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
        'Password must contain uppercase, lowercase, number, and special character'
      ),
    confirmPassword: z.string(),
  })
  .refine(data => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ['confirmPassword'],
  });

// Query Schemas
export const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
});

export const searchSchema = z
  .object({
    q: z.string().optional(),
    category: z.string().optional(),
    tags: z.array(z.string()).optional(),
    status: z.string().optional(),
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).default('desc'),
  })
  .merge(paginationSchema);

// File Upload Schema
export const fileUploadSchema = z.object({
  file: z.any(),
  folder: z.string().optional(),
  public: z.boolean().default(true),
});

// AI Content Schema
export const aiContentRequestSchema = z.object({
  type: z.enum(['title', 'content', 'seo', 'social']),
  context: z.string().min(1, 'Context is required'),
  tone: z.enum(['professional', 'casual', 'technical', 'creative']).default('professional'),
  length: z.enum(['short', 'medium', 'long']).default('medium'),
});

// Export all schemas as a single object for easier imports
export const schemas = {
  // Auth
  login: loginSchema,
  register: registerSchema,
  forgotPassword: forgotPasswordSchema,
  resetPassword: resetPasswordSchema,

  // Blog
  blogPost: blogPostSchema,
  blogCategory: blogCategorySchema,

  // Projects
  project: projectSchema,
  projectCategory: projectCategorySchema,

  // Contact
  contactMessage: contactMessageSchema,

  // Newsletter
  newsletterSubscribe: newsletterSubscribeSchema,
  newsletterCampaign: newsletterCampaignSchema,

  // Settings
  siteSettings: siteSettingsSchema,
  userProfile: userProfileSchema,
  changePassword: changePasswordSchema,

  // Queries
  pagination: paginationSchema,
  search: searchSchema,

  // Files
  fileUpload: fileUploadSchema,

  // AI
  aiContentRequest: aiContentRequestSchema,
} as const;
