const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3002';

async function testSuperAdminRegistration() {
  try {
    console.log('🔧 Testing Super Admin registration...');
    
    const registrationData = {
      firstName: 'Super',
      lastName: 'Admin',
      email: '<EMAIL>',
      password: 'SuperAdmin123!@#'
    };

    console.log('📝 Registering Super Admin with data:', {
      firstName: registrationData.firstName,
      lastName: registrationData.lastName,
      email: registrationData.email,
      password: '***hidden***'
    });

    const response = await fetch(`${API_BASE_URL}/api/auth/register-super-admin`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(registrationData),
    });

    console.log('📊 Response status:', response.status);
    
    const responseData = await response.json();
    console.log('📊 Response data:', JSON.stringify(responseData, null, 2));

    if (!response.ok) {
      console.error('❌ Registration failed:', responseData);
      return false;
    }

    console.log('✅ Super Admin registration successful!');
    console.log('👤 User created:', {
      id: responseData.data.user.id,
      email: responseData.data.user.email,
      name: responseData.data.user.name,
      role: responseData.data.user.role,
      emailVerified: responseData.data.user.emailVerified,
    });

    return true;
  } catch (error) {
    console.error('❌ Error testing Super Admin registration:', error.message);
    return false;
  }
}

async function testRegistrationStatusAfter() {
  try {
    console.log('\n🔧 Testing registration status after Super Admin creation...');
    
    const response = await fetch(`${API_BASE_URL}/api/auth/registration-status`);
    const data = await response.json();
    
    console.log('📊 Registration status:', data);
    
    if (data.data.registrationOpen === false) {
      console.log('✅ Registration correctly closed after Super Admin creation');
    } else {
      console.log('⚠️  Registration still open - this might be an issue');
    }
    
    return true;
  } catch (error) {
    console.error('❌ Error checking registration status:', error.message);
    return false;
  }
}

async function testLogin() {
  try {
    console.log('\n🔧 Testing Super Admin login...');
    
    const loginData = {
      email: '<EMAIL>',
      password: 'SuperAdmin123!@#'
    };

    const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(loginData),
    });

    const responseData = await response.json();
    
    if (!response.ok) {
      console.log('⚠️  Login failed (expected if email not verified):', responseData.error);
      return false;
    }

    console.log('✅ Login successful!');
    console.log('🔑 Access token received:', responseData.data.tokens.accessToken ? 'Yes' : 'No');
    
    return true;
  } catch (error) {
    console.error('❌ Error testing login:', error.message);
    return false;
  }
}

// Run all tests
async function runTests() {
  console.log('🚀 Starting Super Admin Registration Tests\n');
  
  const registrationSuccess = await testSuperAdminRegistration();
  if (registrationSuccess) {
    await testRegistrationStatusAfter();
    await testLogin();
  }
  
  console.log('\n🎉 Tests completed!');
}

runTests();
