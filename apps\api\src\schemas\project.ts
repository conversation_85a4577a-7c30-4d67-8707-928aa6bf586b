import { z } from 'zod';

export const projectSchema = z.object({
  title: z.string()
    .min(1, 'Title is required')
    .max(200, 'Title must be less than 200 characters')
    .trim(),
  
  slug: z.string()
    .min(1, 'Slug is required')
    .max(100, 'Slug must be less than 100 characters')
    .regex(/^[a-z0-9-]+$/, 'Slug must contain only lowercase letters, numbers, and hyphens')
    .optional(),
  
  description: z.string()
    .min(1, 'Description is required')
    .max(500, 'Description must be less than 500 characters')
    .trim(),
  
  content: z.string()
    .min(1, 'Content is required'),
  
  excerpt: z.string()
    .min(1, 'Excerpt is required')
    .max(300, 'Excerpt must be less than 300 characters')
    .trim(),
  
  category: z.enum([
    'web-development',
    'mobile-app',
    'desktop-app',
    'api-development',
    'devops',
    'data-science',
    'machine-learning',
    'blockchain',
    'game-development',
    'other',
  ]),
  
  tags: z.array(z.string().trim().toLowerCase())
    .max(10, 'Maximum 10 tags allowed')
    .default([]),
  
  technologies: z.array(z.string().trim())
    .max(20, 'Maximum 20 technologies allowed')
    .default([]),
  
  status: z.enum(['draft', 'published', 'archived'])
    .default('draft'),
  
  featured: z.boolean()
    .default(false),
  
  images: z.object({
    thumbnail: z.string()
      .url('Thumbnail must be a valid URL'),
    gallery: z.array(z.string().url('Gallery images must be valid URLs'))
      .max(10, 'Maximum 10 gallery images allowed')
      .default([]),
    hero: z.string()
      .url('Hero image must be a valid URL')
      .optional(),
  }),
  
  links: z.object({
    live: z.string()
      .url('Live URL must be valid')
      .optional(),
    github: z.string()
      .url('GitHub URL must be valid')
      .optional(),
    demo: z.string()
      .url('Demo URL must be valid')
      .optional(),
    case_study: z.string()
      .url('Case study URL must be valid')
      .optional(),
  }).default({}),
  
  seo: z.object({
    title: z.string()
      .max(60, 'SEO title must be less than 60 characters')
      .optional(),
    description: z.string()
      .max(160, 'SEO description must be less than 160 characters')
      .optional(),
    keywords: z.array(z.string())
      .max(10, 'Maximum 10 SEO keywords allowed')
      .optional(),
    ogImage: z.string()
      .url('OG image must be a valid URL')
      .optional(),
  }).default({}),
  
  publishedAt: z.string()
    .datetime()
    .optional()
    .or(z.date().optional()),
});

export const updateProjectSchema = projectSchema.partial().extend({
  id: z.string()
    .min(1, 'Project ID is required'),
});

export const projectQuerySchema = z.object({
  page: z.string()
    .regex(/^\d+$/, 'Page must be a number')
    .transform(Number)
    .refine(val => val > 0, 'Page must be greater than 0')
    .default('1'),
  
  limit: z.string()
    .regex(/^\d+$/, 'Limit must be a number')
    .transform(Number)
    .refine(val => val > 0 && val <= 100, 'Limit must be between 1 and 100')
    .default('10'),
  
  category: z.enum([
    'web-development',
    'mobile-app',
    'desktop-app',
    'api-development',
    'devops',
    'data-science',
    'machine-learning',
    'blockchain',
    'game-development',
    'other',
  ]).optional(),
  
  featured: z.enum(['true', 'false'])
    .transform(val => val === 'true')
    .optional(),
  
  status: z.enum(['draft', 'published', 'archived'])
    .optional(),
  
  search: z.string()
    .min(1, 'Search query must not be empty')
    .max(100, 'Search query must be less than 100 characters')
    .optional(),
  
  tags: z.string()
    .transform(val => val.split(',').map(tag => tag.trim().toLowerCase()))
    .optional(),
  
  sort: z.enum(['newest', 'oldest', 'popular', 'featured'])
    .default('newest'),
});

export type ProjectInput = z.infer<typeof projectSchema>;
export type UpdateProjectInput = z.infer<typeof updateProjectSchema>;
export type ProjectQuery = z.infer<typeof projectQuerySchema>;
