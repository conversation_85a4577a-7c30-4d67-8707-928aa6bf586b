const fetch = require('node-fetch');

const ADMIN_BASE_URL = 'http://localhost:3003';

async function testNextAuthLogin() {
  console.log('🔧 Testing NextAuth Login Flow...\n');

  try {
    // Step 1: Get CSRF token
    console.log('1. Getting CSRF token...');
    const csrfResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/csrf`);
    const csrfData = await csrfResponse.json();

    console.log('📊 CSRF Response Status:', csrfResponse.status);
    if (csrfResponse.ok) {
      console.log('✅ CSRF token obtained:', csrfData.csrfToken ? 'Present' : 'Missing');
    } else {
      console.log('❌ Failed to get CSRF token');
      return;
    }

    // Step 2: Attempt login
    console.log('\n2. Attempting login...');
    const loginData = new URLSearchParams({
      email: '<EMAIL>',
      password: 'SuperAdmin123!@#',
      csrfToken: csrfData.csrfToken,
      callbackUrl: `${ADMIN_BASE_URL}/admin`,
      json: 'true',
    });

    const loginResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Cookie: csrfResponse.headers.get('set-cookie') || '',
      },
      body: loginData.toString(),
    });

    console.log('📊 Login Response Status:', loginResponse.status);
    console.log('📊 Login Response Headers:', Object.fromEntries(loginResponse.headers.entries()));

    if (loginResponse.ok) {
      const loginResult = await loginResponse.json();
      console.log('✅ Login successful!');
      console.log('📊 Login Result:', loginResult);
    } else {
      const errorText = await loginResponse.text();
      console.log('❌ Login failed');
      console.log('📊 Error Response:', errorText);
    }

    // Step 3: Check session
    console.log('\n3. Checking session...');

    // Extract session token from set-cookie header
    const setCookieHeader = loginResponse.headers.get('set-cookie') || '';
    console.log('📊 Set-Cookie Header:', setCookieHeader);

    // Parse the session token
    const sessionTokenMatch = setCookieHeader.match(/next-auth\.session-token=([^;]+)/);
    const sessionToken = sessionTokenMatch ? sessionTokenMatch[1] : null;
    console.log('📊 Session Token:', sessionToken ? 'Present' : 'Missing');

    const sessionResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/session`, {
      headers: {
        Cookie: setCookieHeader,
      },
    });

    const sessionData = await sessionResponse.json();
    console.log('📊 Session Response Status:', sessionResponse.status);

    if (sessionResponse.ok && sessionData.user) {
      console.log('✅ Session active!');
      console.log('📊 User Session:', {
        id: sessionData.user.id,
        email: sessionData.user.email,
        name: sessionData.user.name,
        role: sessionData.user.role,
      });
    } else {
      console.log('❌ No active session');
      console.log('📊 Session Data:', sessionData);
    }
  } catch (error) {
    console.error('❌ Error during NextAuth login test:', error.message);
  }
}

async function testDirectAuthFlow() {
  console.log('\n🔧 Testing Direct Auth Flow (Bypass NextAuth)...\n');

  try {
    // Test direct API call to our auth endpoint
    const response = await fetch('http://localhost:3002/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'SuperAdmin123!@#',
      }),
    });

    const data = await response.json();
    console.log('📊 Direct API Response Status:', response.status);

    if (response.ok && data.success) {
      console.log('✅ Direct API login successful!');
      console.log('📊 User Data:', {
        id: data.data.user.id,
        email: data.data.user.email,
        name: data.data.user.name,
        role: data.data.user.role,
        emailVerified: data.data.user.emailVerified,
      });
      console.log('📊 Token Structure:', {
        hasTokens: !!data.data.tokens,
        accessToken: data.data.tokens?.accessToken ? 'Present' : 'Missing',
        refreshToken: data.data.tokens?.refreshToken ? 'Present' : 'Missing',
      });
    } else {
      console.log('❌ Direct API login failed');
      console.log('📊 Error:', data.error || 'Unknown error');
    }
  } catch (error) {
    console.error('❌ Error during direct auth test:', error.message);
  }
}

async function runTests() {
  console.log('🚀 Starting Authentication Flow Tests\n');

  await testDirectAuthFlow();
  await testNextAuthLogin();

  console.log('\n🎉 Authentication tests completed!');
  console.log('\n📝 Debugging Tips:');
  console.log('1. If direct API works but NextAuth fails, check the authorize function in auth.ts');
  console.log('2. Check if the API server is accessible from the admin app');
  console.log('3. Verify the token structure matches what NextAuth expects');
  console.log('4. Check for CORS issues between admin app and API server');
}

runTests();
