'use client';

import { useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  MdDashboard as LayoutDashboard,
  MdArticle as FileText,
  MdMessage as MessageSquare,
  MdPeople as Users,
  MdSettings as Settings,
  MdBarChart as BarChart3,
  MdMail as Mail,
  MdMenu as Menu,
  MdClose as X,
  MdLogo<PERSON> as LogOut,
  <PERSON>d<PERSON><PERSON> as User,
  MdNotifications as Bell,
  MdSearch as Search,
  MdKeyboardArrowDown as ChevronDown,
  MdImage as Image,
} from 'react-icons/md';

const navigation = [
  { name: 'Dashboard', href: '/admin', icon: LayoutDashboard },
  { name: 'Blog Posts', href: '/admin/blog', icon: FileText },
  { name: 'Messages', href: '/admin/messages', icon: MessageSquare },
  { name: 'Analytics', href: '/admin/analytics', icon: BarChart3 },
  { name: 'Media', href: '/admin/media', icon: Image },
  { name: 'Newsletter', href: '/admin/newsletter', icon: Mail },
  { name: 'Users', href: '/admin/users', icon: Users },
  { name: 'Settings', href: '/admin/settings', icon: Settings },
];

interface ModernAdminLayoutProps {
  children: React.ReactNode;
}

export function ModernAdminLayout({ children }: ModernAdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const pathname = usePathname();
  const { data: session } = useSession();

  // Close mobile sidebar when route changes
  useEffect(() => {
    setSidebarOpen(false);
  }, [pathname]);

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => setUserMenuOpen(false);
    if (userMenuOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [userMenuOpen]);

  const handleSignOut = () => {
    signOut({ callbackUrl: '/auth/signin' });
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'bg-red-100 text-red-800';
      case 'admin':
        return 'bg-blue-100 text-blue-800';
      case 'editor':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatRole = (role: string) => {
    return role
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--color-gray-50)' }}>
      {/* Mobile sidebar backdrop */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 lg:hidden animate-fade-in"
          style={{ zIndex: 'var(--z-modal-backdrop)' }}
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Mobile sidebar */}
      <div
        className={`fixed inset-y-0 left-0 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:hidden ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
        style={{ zIndex: 'var(--z-modal)' }}
      >
        <div className="flex h-full flex-col">
          {/* Mobile header */}
          <div
            className="flex h-16 items-center justify-between px-6 border-b"
            style={{ borderColor: 'var(--color-gray-200)' }}
          >
            <div className="flex items-center space-x-3">
              <div
                className="w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold"
                style={{ backgroundColor: 'var(--color-primary-600)' }}
              >
                R
              </div>
              <h1 className="text-xl font-bold" style={{ color: 'var(--color-gray-900)' }}>
                RxY Admin
              </h1>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="p-2 rounded-md hover:bg-gray-100 transition-colors"
            >
              <X className="h-5 w-5" style={{ color: 'var(--color-gray-500)' }} />
            </button>
          </div>

          {/* Mobile navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map(item => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                    isActive ? 'text-white shadow-md' : 'hover:bg-gray-50'
                  }`}
                  style={{
                    backgroundColor: isActive ? 'var(--color-primary-600)' : 'transparent',
                    color: isActive ? 'white' : 'var(--color-gray-700)',
                  }}
                >
                  <item.icon
                    className={`mr-3 h-5 w-5 transition-colors ${
                      isActive ? 'text-white' : 'text-gray-400 group-hover:text-gray-600'
                    }`}
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div
          className="flex flex-col flex-grow bg-white border-r"
          style={{ borderColor: 'var(--color-gray-200)' }}
        >
          {/* Desktop header */}
          <div
            className="flex items-center h-16 px-6 border-b"
            style={{ borderColor: 'var(--color-gray-200)' }}
          >
            <div className="flex items-center space-x-3">
              <div
                className="w-8 h-8 rounded-lg flex items-center justify-center text-white font-bold"
                style={{ backgroundColor: 'var(--color-primary-600)' }}
              >
                R
              </div>
              <h1 className="text-xl font-bold" style={{ color: 'var(--color-gray-900)' }}>
                RxY Admin
              </h1>
            </div>
          </div>

          {/* Desktop navigation */}
          <nav className="flex-1 px-4 py-6 space-y-2">
            {navigation.map(item => {
              const isActive = pathname === item.href;
              return (
                <Link
                  key={item.name}
                  href={item.href}
                  className={`group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                    isActive ? 'text-white shadow-md' : 'hover:bg-gray-50'
                  }`}
                  style={{
                    backgroundColor: isActive ? 'var(--color-primary-600)' : 'transparent',
                    color: isActive ? 'white' : 'var(--color-gray-700)',
                  }}
                >
                  <item.icon
                    className={`mr-3 h-5 w-5 transition-colors ${
                      isActive ? 'text-white' : 'text-gray-400 group-hover:text-gray-600'
                    }`}
                  />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* User info at bottom */}
          <div className="p-4 border-t" style={{ borderColor: 'var(--color-gray-200)' }}>
            <div className="flex items-center space-x-3">
              <div
                className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                style={{ backgroundColor: 'var(--color-primary-500)' }}
              >
                {session?.user?.name?.charAt(0) || 'U'}
              </div>
              <div className="flex-1 min-w-0">
                <p
                  className="text-sm font-medium truncate"
                  style={{ color: 'var(--color-gray-900)' }}
                >
                  {session?.user?.name}
                </p>
                <p className="text-xs truncate" style={{ color: 'var(--color-gray-500)' }}>
                  {session?.user?.email}
                </p>
              </div>
            </div>
            {session?.user?.role && (
              <div className="mt-2">
                <span
                  className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(
                    session.user.role
                  )}`}
                >
                  {formatRole(session.user.role)}
                </span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col min-h-screen">
        {/* Top navigation */}
        <header
          className="sticky top-0 bg-white border-b shadow-sm"
          style={{
            borderColor: 'var(--color-gray-200)',
            zIndex: 'var(--z-sticky)',
          }}
        >
          <div className="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
            {/* Mobile menu button */}
            <button
              onClick={() => setSidebarOpen(true)}
              className="p-2 rounded-md hover:bg-gray-100 transition-colors lg:hidden"
            >
              <Menu className="h-6 w-6" style={{ color: 'var(--color-gray-500)' }} />
            </button>

            {/* Search bar - hidden on mobile */}
            <div className="hidden md:flex flex-1 max-w-lg mx-8">
              <div className="relative w-full">
                <Search
                  className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4"
                  style={{ color: 'var(--color-gray-400)' }}
                />
                <input
                  type="text"
                  placeholder="Search..."
                  className="input pl-10 py-2"
                  style={{
                    backgroundColor: 'var(--color-gray-50)',
                    border: '1px solid var(--color-gray-200)',
                  }}
                />
              </div>
            </div>

            {/* Right side actions */}
            <div className="flex items-center space-x-4">
              {/* Notifications */}
              <button className="p-2 rounded-md hover:bg-gray-100 transition-colors relative">
                <Bell className="h-5 w-5" style={{ color: 'var(--color-gray-500)' }} />
                <span
                  className="absolute -top-1 -right-1 h-4 w-4 rounded-full text-xs flex items-center justify-center text-white"
                  style={{ backgroundColor: 'var(--color-accent-500)' }}
                >
                  3
                </span>
              </button>

              {/* User menu */}
              <div className="relative">
                <button
                  onClick={e => {
                    e.stopPropagation();
                    setUserMenuOpen(!userMenuOpen);
                  }}
                  className="flex items-center space-x-2 p-2 rounded-md hover:bg-gray-100 transition-colors"
                >
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                    style={{ backgroundColor: 'var(--color-primary-500)' }}
                  >
                    {session?.user?.name?.charAt(0) || 'U'}
                  </div>
                  <ChevronDown className="h-4 w-4" style={{ color: 'var(--color-gray-500)' }} />
                </button>

                {/* User dropdown */}
                {userMenuOpen && (
                  <div
                    className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border animate-fade-in"
                    style={{
                      borderColor: 'var(--color-gray-200)',
                      zIndex: 'var(--z-dropdown)',
                    }}
                  >
                    <div className="p-3 border-b" style={{ borderColor: 'var(--color-gray-200)' }}>
                      <p className="text-sm font-medium" style={{ color: 'var(--color-gray-900)' }}>
                        {session?.user?.name}
                      </p>
                      <p className="text-xs" style={{ color: 'var(--color-gray-500)' }}>
                        {session?.user?.email}
                      </p>
                    </div>
                    <div className="py-1">
                      <Link
                        href="/admin/profile"
                        className="flex items-center px-3 py-2 text-sm hover:bg-gray-50 transition-colors"
                        style={{ color: 'var(--color-gray-700)' }}
                      >
                        <User className="mr-2 h-4 w-4" />
                        Profile
                      </Link>
                      <Link
                        href="/admin/settings"
                        className="flex items-center px-3 py-2 text-sm hover:bg-gray-50 transition-colors"
                        style={{ color: 'var(--color-gray-700)' }}
                      >
                        <Settings className="mr-2 h-4 w-4" />
                        Settings
                      </Link>
                      <hr style={{ borderColor: 'var(--color-gray-200)' }} />
                      <button
                        onClick={handleSignOut}
                        className="flex items-center w-full px-3 py-2 text-sm hover:bg-gray-50 transition-colors"
                        style={{ color: 'var(--color-gray-700)' }}
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        Sign out
                      </button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="flex-1 bg-gray-50">
          <div className="p-6">
            <div className="max-w-7xl mx-auto">{children}</div>
          </div>
        </main>
      </div>
    </div>
  );
}
