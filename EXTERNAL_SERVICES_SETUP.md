# External Services Configuration Guide

This guide provides step-by-step instructions for setting up all external services required for the RxY.dev platform.

## Overview

The RxY.dev platform requires the following external services:
- MongoDB Atlas (Database)
- Cloudinary (Media Management)
- NextAuth (Authentication)
- Email Service (SMTP/Nodemailer)
- AI Services (OpenAI/Anthropic)

## 1. MongoDB Atlas Setup

### Step 1: Create MongoDB Atlas Account
1. Visit [MongoDB Atlas](https://www.mongodb.com/cloud/atlas)
2. Sign up for a free account or log in
3. Create a new project named "RxY-Platform"

### Step 2: Create Database Cluster
1. Click "Build a Database"
2. Choose "M0 Sandbox" (Free tier)
3. Select your preferred cloud provider and region
4. Name your cluster "rxy-cluster"
5. Click "Create Cluster"

### Step 3: Configure Database Access
1. Go to "Database Access" in the left sidebar
2. Click "Add New Database User"
3. Choose "Password" authentication
4. Create username and secure password
5. Set user privileges to "Read and write to any database"
6. Click "Add User"

### Step 4: Configure Network Access
1. Go to "Network Access" in the left sidebar
2. Click "Add IP Address"
3. Choose "Allow Access from Anywhere" (0.0.0.0/0) for development
4. For production, add specific IP addresses
5. Click "Confirm"

### Step 5: Get Connection String
1. Go to "Database" and click "Connect" on your cluster
2. Choose "Connect your application"
3. Select "Node.js" and version "4.1 or later"
4. Copy the connection string
5. Replace `<password>` with your database user password

### Environment Variables
```env
# MongoDB Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/rxy-platform?retryWrites=true&w=majority
```

## 2. Cloudinary Setup

### Step 1: Create Cloudinary Account
1. Visit [Cloudinary](https://cloudinary.com/)
2. Sign up for a free account
3. Verify your email address

### Step 2: Get API Credentials
1. Go to your Cloudinary Dashboard
2. Find your account details:
   - Cloud Name
   - API Key
   - API Secret

### Step 3: Configure Upload Presets
1. Go to Settings > Upload
2. Click "Add upload preset"
3. Set preset name: "rxy-platform"
4. Set signing mode to "Unsigned"
5. Configure folder: "rxy-uploads"
6. Set allowed formats: jpg, png, gif, webp, svg
7. Enable auto-optimization and quality auto
8. Save preset

### Environment Variables
```env
# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
CLOUDINARY_UPLOAD_PRESET=rxy-platform
```

## 3. NextAuth Configuration

### Step 1: Generate NextAuth Secret
```bash
# Generate a secure random string
openssl rand -base64 32
```

### Step 2: Configure OAuth Providers (Optional)
For Google OAuth:
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `http://localhost:3000/api/auth/callback/google` (development)
   - `http://localhost:3003/api/auth/callback/google` (admin)
   - Your production URLs

### Environment Variables
```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-generated-secret-key

# OAuth Providers (Optional)
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## 4. Email Service Setup

### Option A: Gmail SMTP
1. Enable 2-factor authentication on your Gmail account
2. Generate an App Password:
   - Go to Google Account settings
   - Security > 2-Step Verification > App passwords
   - Generate password for "Mail"

### Option B: SendGrid
1. Sign up at [SendGrid](https://sendgrid.com/)
2. Verify your sender identity
3. Create an API key with Mail Send permissions

### Option C: Mailgun
1. Sign up at [Mailgun](https://www.mailgun.com/)
2. Add and verify your domain
3. Get your API key and domain from the dashboard

### Environment Variables
```env
# Email Configuration (Choose one)

# Gmail SMTP
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# SendGrid
SENDGRID_API_KEY=your-sendgrid-api-key
EMAIL_FROM=<EMAIL>

# Mailgun
MAILGUN_API_KEY=your-mailgun-api-key
MAILGUN_DOMAIN=your-mailgun-domain
EMAIL_FROM=<EMAIL>
```

## 5. AI Services Setup

### Option A: OpenAI
1. Sign up at [OpenAI](https://platform.openai.com/)
2. Add payment method (required for API access)
3. Generate an API key
4. Set usage limits to control costs

### Option B: Anthropic Claude
1. Sign up at [Anthropic](https://console.anthropic.com/)
2. Request API access (may require approval)
3. Generate an API key

### Environment Variables
```env
# AI Services Configuration (Choose one or both)

# OpenAI
OPENAI_API_KEY=sk-your-openai-api-key
OPENAI_MODEL=gpt-4

# Anthropic Claude
ANTHROPIC_API_KEY=your-anthropic-api-key
ANTHROPIC_MODEL=claude-3-sonnet-20240229
```

## 6. Complete Environment Configuration

### Client Application (.env.local)
```env
# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3002

# App Configuration
NEXT_PUBLIC_APP_NAME=RxY.dev
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Cloudinary (Public)
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your-cloud-name
NEXT_PUBLIC_CLOUDINARY_UPLOAD_PRESET=rxy-platform
```

### Admin Application (.env.local)
```env
# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3003
NEXTAUTH_SECRET=your-super-secret-nextauth-secret-change-in-production

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:3002

# App Configuration
NEXT_PUBLIC_APP_NAME=RxY Admin Portal
NEXT_PUBLIC_APP_URL=http://localhost:3003
```

### API Application (.env)
```env
# Server Configuration
PORT=3002
NODE_ENV=development

# Database
MONGODB_URI=mongodb+srv://username:<EMAIL>/rxy-platform?retryWrites=true&w=majority

# JWT
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=7d

# Cloudinary
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret

# Email
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>

# AI Services
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key

# CORS
CORS_ORIGIN=http://localhost:3000,http://localhost:3003
```

## 7. Security Considerations

### Development vs Production
- Use different databases for development and production
- Generate new secrets for production
- Restrict IP access in production
- Use environment-specific API keys
- Enable SSL/TLS in production

### API Keys Security
- Never commit API keys to version control
- Use environment variables for all secrets
- Rotate keys regularly
- Monitor usage and set alerts
- Use least privilege access

## 8. Testing Configuration

After setting up all services, test the configuration:

1. **Database Connection**: Check MongoDB connection in API logs
2. **Image Upload**: Test Cloudinary upload in admin panel
3. **Email Sending**: Test contact form submissions
4. **AI Integration**: Test AI-assisted features
5. **Authentication**: Test login/logout flows

## 9. Troubleshooting

### Common Issues
- **MongoDB Connection**: Check IP whitelist and credentials
- **Cloudinary Upload**: Verify upload preset and API keys
- **Email Delivery**: Check SMTP settings and authentication
- **CORS Errors**: Verify CORS_ORIGIN configuration
- **NextAuth Issues**: Check NEXTAUTH_URL and secret

### Debug Commands
```bash
# Test MongoDB connection
node -e "const mongoose = require('mongoose'); mongoose.connect('your-mongodb-uri').then(() => console.log('Connected')).catch(console.error)"

# Test API endpoints
curl http://localhost:3002/api/health

# Check environment variables
npm run env:check
```

## 10. Production Deployment

When deploying to production:
1. Update all URLs to production domains
2. Use production database clusters
3. Configure production email settings
4. Set up monitoring and logging
5. Enable SSL certificates
6. Configure CDN for static assets
7. Set up backup strategies
