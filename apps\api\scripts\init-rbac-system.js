const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/rxy-dev';

async function initializeRBACSystem() {
  try {
    console.log('🚀 Initializing RBAC System for RxY.dev Platform...');
    console.log('🔌 Connecting to database...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to database');

    // Clear existing data
    console.log('🗑️  Clearing existing data...');
    const collections = await mongoose.connection.db.listCollections().toArray();
    
    for (const collection of collections) {
      await mongoose.connection.db.dropCollection(collection.name);
      console.log(`   ✅ Dropped collection: ${collection.name}`);
    }

    // Define Permission Template Schema
    const permissionTemplateSchema = new mongoose.Schema({
      _id: String,
      name: String,
      description: String,
      resource: String,
      actions: [String],
      level: Number,
      isDefault: Boolean,
    }, { timestamps: true, versionKey: false });

    const PermissionTemplate = mongoose.model('PermissionTemplate', permissionTemplateSchema);

    // Create default permission templates
    console.log('📋 Creating default permission templates...');
    
    const defaultPermissions = [
      // Super Admin permissions
      {
        _id: 'perm_super_admin_users',
        name: 'Super Admin - User Management',
        description: 'Complete user management capabilities',
        resource: 'users',
        actions: ['read', 'write', 'delete', 'manage'],
        level: 1,
        isDefault: true,
      },
      {
        _id: 'perm_super_admin_blog',
        name: 'Super Admin - Blog Management',
        description: 'Full blog management capabilities',
        resource: 'blog',
        actions: ['read', 'write', 'delete', 'publish', 'moderate'],
        level: 1,
        isDefault: true,
      },
      {
        _id: 'perm_super_admin_projects',
        name: 'Super Admin - Project Management',
        description: 'Full project management capabilities',
        resource: 'projects',
        actions: ['read', 'write', 'delete', 'publish'],
        level: 1,
        isDefault: true,
      },
      {
        _id: 'perm_super_admin_analytics',
        name: 'Super Admin - Analytics Access',
        description: 'Full analytics and reporting access',
        resource: 'analytics',
        actions: ['read', 'export', 'manage'],
        level: 1,
        isDefault: true,
      },
      {
        _id: 'perm_super_admin_settings',
        name: 'Super Admin - System Settings',
        description: 'System configuration and settings',
        resource: 'settings',
        actions: ['read', 'write', 'manage'],
        level: 1,
        isDefault: true,
      },
      
      // Admin permissions
      {
        _id: 'perm_admin_blog',
        name: 'Admin - Blog Management',
        description: 'Blog management with publishing rights',
        resource: 'blog',
        actions: ['read', 'write', 'publish', 'moderate'],
        level: 2,
        isDefault: true,
      },
      {
        _id: 'perm_admin_messages',
        name: 'Admin - Message Management',
        description: 'Message and contact form management',
        resource: 'messages',
        actions: ['read', 'write', 'moderate'],
        level: 2,
        isDefault: true,
      },
      {
        _id: 'perm_admin_analytics',
        name: 'Admin - Analytics Access',
        description: 'View analytics and reports',
        resource: 'analytics',
        actions: ['read', 'export'],
        level: 2,
        isDefault: true,
      },
      {
        _id: 'perm_admin_comments',
        name: 'Admin - Comment Moderation',
        description: 'Moderate blog comments',
        resource: 'comments',
        actions: ['read', 'moderate'],
        level: 2,
        isDefault: true,
      },
      
      // Editor permissions
      {
        _id: 'perm_editor_blog',
        name: 'Editor - Blog Writing',
        description: 'Create and edit blog posts',
        resource: 'blog',
        actions: ['read', 'write'],
        level: 3,
        isDefault: true,
      },
      {
        _id: 'perm_editor_comments',
        name: 'Editor - Comment Moderation',
        description: 'Moderate blog comments',
        resource: 'comments',
        actions: ['read', 'moderate'],
        level: 3,
        isDefault: true,
      },
      {
        _id: 'perm_editor_media',
        name: 'Editor - Media Management',
        description: 'Upload and manage media files',
        resource: 'media',
        actions: ['read', 'write'],
        level: 3,
        isDefault: true,
      },
    ];

    await PermissionTemplate.insertMany(defaultPermissions);
    console.log(`   ✅ Created ${defaultPermissions.length} permission templates`);

    // Create indexes
    console.log('📊 Creating database indexes...');
    
    // Permission Template indexes
    await PermissionTemplate.collection.createIndex({ resource: 1, level: 1 });
    await PermissionTemplate.collection.createIndex({ isDefault: 1 });
    
    console.log('   ✅ Permission template indexes created');

    console.log('🎉 RBAC System initialized successfully!');
    console.log('');
    console.log('📝 Next Steps:');
    console.log('   1. Start the API server: npm run dev:api');
    console.log('   2. Register the first Super Admin at: POST /api/auth/register-super-admin');
    console.log('   3. Verify the Super Admin email');
    console.log('   4. Login and start creating admin users');
    console.log('');
    console.log('🔐 Security Features Enabled:');
    console.log('   ✅ Role-based access control (Super Admin, Admin, Editor)');
    console.log('   ✅ Permission-based resource access');
    console.log('   ✅ Email verification required');
    console.log('   ✅ Registration disabled after first Super Admin');
    console.log('   ✅ Account status management');
    console.log('   ✅ Separate comment system for blog visitors');
    console.log('');

  } catch (error) {
    console.error('❌ Error initializing RBAC system:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// Run the initialization
initializeRBACSystem();
