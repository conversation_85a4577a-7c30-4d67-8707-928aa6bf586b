const mongoose = require('mongoose');
const dotenv = require('dotenv');

// Load environment variables
dotenv.config();

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/rxy-dev';

async function resetDatabase() {
  try {
    console.log('🔌 Connecting to database...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to database');

    // Get all collections
    const collections = await mongoose.connection.db.listCollections().toArray();
    
    console.log('🗑️  Clearing database...');
    
    // Drop all collections
    for (const collection of collections) {
      await mongoose.connection.db.dropCollection(collection.name);
      console.log(`   ✅ Dropped collection: ${collection.name}`);
    }

    console.log('✅ Database cleared successfully!');
    console.log('📝 Ready for new RBAC system implementation');

  } catch (error) {
    console.error('❌ Error resetting database:', error.message);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from database');
  }
}

// Run the script
resetDatabase();
