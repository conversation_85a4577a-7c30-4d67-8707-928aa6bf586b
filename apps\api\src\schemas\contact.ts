import { z } from 'zod';

export const contactSchema = z.object({
  name: z.string()
    .min(1, 'Name is required')
    .max(100, 'Name must be less than 100 characters')
    .trim()
    .regex(/^[a-zA-Z\s'-]+$/, 'Name must contain only letters, spaces, hyphens, and apostrophes'),
  
  email: z.string()
    .min(1, 'Email is required')
    .email('Please enter a valid email address')
    .max(255, 'Email must be less than 255 characters')
    .toLowerCase()
    .trim(),
  
  subject: z.string()
    .min(1, 'Subject is required')
    .max(200, 'Subject must be less than 200 characters')
    .trim(),
  
  message: z.string()
    .min(10, 'Message must be at least 10 characters long')
    .max(5000, 'Message must be less than 5000 characters')
    .trim(),
  
  type: z.enum([
    'general',
    'project',
    'consulting',
    'speaking',
    'media',
    'support',
  ]).default('general'),
  
  priority: z.enum(['low', 'medium', 'high', 'urgent'])
    .default('medium'),
  
  // Optional fields for enhanced contact forms
  company: z.string()
    .max(100, 'Company name must be less than 100 characters')
    .trim()
    .optional(),
  
  phone: z.string()
    .regex(/^[\+]?[1-9][\d]{0,15}$/, 'Please enter a valid phone number')
    .optional(),
  
  website: z.string()
    .url('Please enter a valid website URL')
    .optional(),
  
  budget: z.enum([
    'under-5k',
    '5k-10k',
    '10k-25k',
    '25k-50k',
    '50k-100k',
    'over-100k',
    'not-specified',
  ]).optional(),
  
  timeline: z.enum([
    'asap',
    '1-month',
    '2-3-months',
    '3-6-months',
    '6-12-months',
    'flexible',
  ]).optional(),
  
  // Honeypot field for spam protection
  honeypot: z.string()
    .max(0, 'Spam detected')
    .optional(),
  
  // Consent and privacy
  consent: z.boolean()
    .refine(val => val === true, 'You must agree to the privacy policy'),
  
  newsletter: z.boolean()
    .default(false),
});

export const messageUpdateSchema = z.object({
  status: z.enum(['unread', 'read', 'replied', 'archived']),
  
  priority: z.enum(['low', 'medium', 'high', 'urgent']),
  
  tags: z.array(z.string().trim().toLowerCase())
    .max(10, 'Maximum 10 tags allowed'),
  
  assignedTo: z.string()
    .regex(/^[0-9a-fA-F]{24}$/, 'Invalid user ID format')
    .optional(),
  
  notes: z.string()
    .max(1000, 'Notes must be less than 1000 characters')
    .optional(),
  
  followUp: z.object({
    scheduled: z.boolean(),
    date: z.string().datetime().optional(),
    notes: z.string().max(500, 'Follow-up notes must be less than 500 characters').optional(),
  }).optional(),
});

export const messageReplySchema = z.object({
  content: z.string()
    .min(1, 'Reply content is required')
    .max(5000, 'Reply must be less than 5000 characters')
    .trim(),
  
  method: z.enum(['email', 'phone', 'meeting'])
    .default('email'),
  
  sendEmail: z.boolean()
    .default(true),
  
  emailTemplate: z.string()
    .optional(),
  
  ccEmails: z.array(z.string().email('Invalid email address'))
    .max(5, 'Maximum 5 CC emails allowed')
    .default([]),
  
  attachments: z.array(z.object({
    filename: z.string(),
    url: z.string().url(),
    size: z.number().positive(),
    type: z.string(),
  }))
    .max(5, 'Maximum 5 attachments allowed')
    .default([]),
});

export const messageQuerySchema = z.object({
  page: z.string()
    .regex(/^\d+$/, 'Page must be a number')
    .transform(Number)
    .refine(val => val > 0, 'Page must be greater than 0')
    .default('1'),
  
  limit: z.string()
    .regex(/^\d+$/, 'Limit must be a number')
    .transform(Number)
    .refine(val => val > 0 && val <= 100, 'Limit must be between 1 and 100')
    .default('20'),
  
  status: z.enum(['unread', 'read', 'replied', 'archived'])
    .optional(),
  
  type: z.enum(['general', 'project', 'consulting', 'speaking', 'media', 'support'])
    .optional(),
  
  priority: z.enum(['low', 'medium', 'high', 'urgent'])
    .optional(),
  
  assignedTo: z.string()
    .regex(/^[0-9a-fA-F]{24}$/, 'Invalid user ID format')
    .optional(),
  
  search: z.string()
    .min(1, 'Search query must not be empty')
    .max(100, 'Search query must be less than 100 characters')
    .optional(),
  
  tags: z.string()
    .transform(val => val.split(',').map(tag => tag.trim().toLowerCase()))
    .optional(),
  
  dateFrom: z.string()
    .datetime()
    .optional(),
  
  dateTo: z.string()
    .datetime()
    .optional(),
  
  sort: z.enum(['newest', 'oldest', 'priority', 'status'])
    .default('newest'),
});

export type ContactInput = z.infer<typeof contactSchema>;
export type MessageUpdateInput = z.infer<typeof messageUpdateSchema>;
export type MessageReplyInput = z.infer<typeof messageReplySchema>;
export type MessageQuery = z.infer<typeof messageQuerySchema>;
