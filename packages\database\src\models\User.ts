import mongoose, { Schema, Document, Model } from 'mongoose';
import bcrypt from 'bcryptjs';
import * as jwt from 'jsonwebtoken';
import { nanoid } from 'nanoid';
import * as crypto from 'crypto';

// Enhanced Permission System
export interface IPermission {
  resource: string; // e.g., 'blog', 'projects', 'users', 'analytics'
  actions: string[]; // e.g., ['read', 'write', 'delete', 'publish']
}

export interface IRole {
  name: string;
  level: number; // 1 = Super Admin, 2 = Admin, 3 = Editor, etc.
  permissions: IPermission[];
  description?: string;
}

export interface IUser extends Document {
  _id: string;
  email: string;
  firstName: string;
  lastName: string;
  name: string;
  password: string;
  refreshTokens: string[];
  emailVerified: boolean;
  emailVerificationToken?: string;
  emailVerificationExpires?: Date;
  passwordResetToken?: string;
  passwordResetExpires?: Date;
  lastLoginAt?: Date;
  loginAttempts: number;
  lockUntil?: Date;

  // Enhanced RBAC fields
  role: 'super_admin' | 'admin' | 'editor' | 'user';
  permissions: IPermission[];
  isSuperAdmin: boolean;
  isFirstUser: boolean;
  createdBy?: string; // ID of the user who created this account
  status: 'active' | 'inactive' | 'suspended';

  // Profile fields
  avatar?: string;
  bio?: string;
  timezone?: string;

  // Security fields
  twoFactorEnabled: boolean;
  twoFactorSecret?: string;
  backupCodes?: string[];
  lastPasswordChange?: Date;
  passwordHistory: string[]; // Store hashed passwords to prevent reuse

  // Timestamps
  createdAt: Date;
  updatedAt: Date;

  // Methods
  comparePassword(candidatePassword: string): Promise<boolean>;
  generateAccessToken(): string;
  generateRefreshToken(): string;
  addRefreshToken(token: string): Promise<void>;
  removeRefreshToken(token: string): Promise<void>;
  clearRefreshTokens(): Promise<void>;
  isLocked(): boolean;
  incrementLoginAttempts(): Promise<void>;
  resetLoginAttempts(): Promise<void>;
  hasPermission(resource: string, action: string): boolean;
  canManageUser(targetUser: IUser): boolean;
  generateEmailVerificationToken(): string;
  generatePasswordResetToken(): string;
}

export interface IUserModel extends Model<IUser> {
  findByEmail(email: string): Promise<IUser | null>;
  findByResetToken(token: string): Promise<IUser | null>;
  findByVerificationToken(token: string): Promise<IUser | null>;
}

const userSchema = new Schema<IUser>(
  {
    _id: {
      type: String,
      default: () => nanoid(12), // Use nanoid for user IDs
    },
    email: {
      type: String,
      required: true,
      lowercase: true,
      trim: true,
      unique: true,
    },
    firstName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
    },
    lastName: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    password: {
      type: String,
      required: true,
      minlength: 8,
    },
    role: {
      type: String,
      enum: ['super_admin', 'admin', 'editor', 'user'],
      default: 'user',
    },
    permissions: [
      {
        resource: {
          type: String,
          required: true,
        },
        actions: [
          {
            type: String,
            required: true,
          },
        ],
      },
    ],
    isSuperAdmin: {
      type: Boolean,
      default: false,
    },
    isFirstUser: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: String,
      ref: 'User',
      default: null,
    },
    status: {
      type: String,
      enum: ['active', 'inactive', 'suspended'],
      default: 'active',
    },
    avatar: {
      type: String,
      default: null,
    },
    bio: {
      type: String,
      maxlength: 500,
      default: null,
    },
    timezone: {
      type: String,
      default: 'UTC',
    },
    refreshTokens: [String],
    emailVerified: {
      type: Boolean,
      default: false,
    },
    emailVerificationToken: {
      type: String,
      default: null,
    },
    emailVerificationExpires: {
      type: Date,
      default: null,
    },
    passwordResetToken: {
      type: String,
      default: null,
    },
    passwordResetExpires: {
      type: Date,
      default: null,
    },
    lastLoginAt: {
      type: Date,
      default: null,
    },
    loginAttempts: {
      type: Number,
      default: 0,
    },
    lockUntil: {
      type: Date,
      default: null,
    },
    twoFactorEnabled: {
      type: Boolean,
      default: false,
    },
    twoFactorSecret: {
      type: String,
      default: null,
    },
    backupCodes: [String],
    lastPasswordChange: {
      type: Date,
      default: Date.now,
    },
    passwordHistory: [String], // Store last 5 password hashes
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

// Indexes
userSchema.index({ email: 1 }, { unique: true });
userSchema.index({ emailVerificationToken: 1 });
userSchema.index({ passwordResetToken: 1 });
userSchema.index({ createdAt: -1 });

// Virtual for id
userSchema.virtual('id').get(function () {
  return this._id;
});

// Ensure virtual fields are serialized
userSchema.set('toJSON', {
  virtuals: true,
  transform: function (doc, ret) {
    delete ret._id;
    delete ret.__v;
    delete ret.password;
    delete ret.refreshTokens;
    delete ret.emailVerificationToken;
    delete ret.passwordResetToken;
    delete ret.passwordResetExpires;
    delete ret.loginAttempts;
    delete ret.lockUntil;
    return ret;
  },
});

// Pre-save middleware to hash password
userSchema.pre('save', async function (next) {
  if (!this.isModified('password')) return next();

  try {
    const salt = await bcrypt.genSalt(12);
    this.password = await bcrypt.hash(this.password, salt);
    next();
  } catch (error) {
    next(error as Error);
  }
});

// Instance methods
userSchema.methods.comparePassword = async function (candidatePassword: string): Promise<boolean> {
  return bcrypt.compare(candidatePassword, this.password);
};

userSchema.methods.hasPermission = function (resource: string, action: string): boolean {
  // Super admins have all permissions
  if (this.isSuperAdmin) return true;

  // Check specific permissions
  return this.permissions.some(
    (permission: IPermission) =>
      permission.resource === resource && permission.actions.includes(action)
  );
};

userSchema.methods.canManageUser = function (targetUser: IUser): boolean {
  // Super admins can manage anyone except other super admins
  if (this.isSuperAdmin) {
    return !targetUser.isSuperAdmin || this._id === targetUser._id;
  }

  // Admins can only manage users with lower or equal role levels
  const roleLevels: Record<string, number> = {
    super_admin: 1,
    admin: 2,
    editor: 3,
    user: 4,
  };

  const thisLevel = roleLevels[this.role] || 999;
  const targetLevel = roleLevels[targetUser.role] || 999;

  return thisLevel <= targetLevel && this._id !== targetUser._id;
};

userSchema.methods.generateEmailVerificationToken = function (): string {
  const token = crypto.randomBytes(32).toString('hex');
  this.emailVerificationToken = token;
  this.emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
  return token;
};

userSchema.methods.generatePasswordResetToken = function (): string {
  const token = crypto.randomBytes(32).toString('hex');
  this.passwordResetToken = token;
  this.passwordResetExpires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
  return token;
};

userSchema.methods.generateAccessToken = function (): string {
  const payload = {
    id: this._id,
    email: this.email,
    role: this.role,
  };

  return (jwt.sign as any)(payload, process.env.JWT_ACCESS_SECRET || 'access-secret', {
    expiresIn: process.env.JWT_ACCESS_EXPIRES || '15m',
  });
};

userSchema.methods.generateRefreshToken = function (): string {
  const payload = {
    id: this._id,
    type: 'refresh',
  };

  return (jwt.sign as any)(payload, process.env.JWT_REFRESH_SECRET || 'refresh-secret', {
    expiresIn: process.env.JWT_REFRESH_EXPIRES || '7d',
  });
};

userSchema.methods.addRefreshToken = async function (token: string): Promise<void> {
  this.refreshTokens.push(token);
  await this.save();
};

userSchema.methods.removeRefreshToken = async function (token: string): Promise<void> {
  this.refreshTokens = this.refreshTokens.filter((t: string) => t !== token);
  await this.save();
};

userSchema.methods.clearRefreshTokens = async function (): Promise<void> {
  this.refreshTokens = [];
  await this.save();
};

userSchema.methods.isLocked = function (): boolean {
  return !!(this.lockUntil && this.lockUntil > new Date());
};

userSchema.methods.incrementLoginAttempts = async function (): Promise<void> {
  const maxAttempts = 5;
  const lockTime = 2 * 60 * 60 * 1000; // 2 hours

  // If we have a previous lock that has expired, restart at 1
  if (this.lockUntil && this.lockUntil < new Date()) {
    return this.updateOne({
      $unset: { lockUntil: 1 },
      $set: { loginAttempts: 1 },
    });
  }

  const updates: any = { $inc: { loginAttempts: 1 } };

  // If we've reached max attempts and it's not locked yet, lock the account
  if (this.loginAttempts + 1 >= maxAttempts && !this.isLocked()) {
    updates.$set = { lockUntil: new Date(Date.now() + lockTime) };
  }

  await this.updateOne(updates);
};

userSchema.methods.resetLoginAttempts = async function (): Promise<void> {
  await this.updateOne({
    $unset: { loginAttempts: 1, lockUntil: 1 },
  });
};

// Static methods
userSchema.statics.findByEmail = function (email: string) {
  return this.findOne({ email: email.toLowerCase() });
};

userSchema.statics.findByResetToken = function (token: string) {
  return this.findOne({
    passwordResetToken: token,
    passwordResetExpires: { $gt: new Date() },
  });
};

userSchema.statics.findByVerificationToken = function (token: string) {
  return this.findOne({ emailVerificationToken: token });
};

export const User: IUserModel = mongoose.model<IUser, IUserModel>('User', userSchema);
