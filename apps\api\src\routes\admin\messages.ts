import { Router } from 'express';
import { authenticate, authorize } from '@/middleware/auth';
import { validateBody, validateQuery } from '@/middleware/validation';
import { asyncHandler, createError } from '@/middleware/errorHandler';
import { HTTP_STATUS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '@rxy/shared';

const router = Router();

// All admin routes require authentication and admin role
router.use(authenticate);
router.use(authorize(['super_admin', 'admin']));

// Mock messages data for development
const mockMessages = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    subject: 'Project Collaboration Inquiry',
    message:
      "Hi, I came across your portfolio and I'm impressed with your work on React applications. I have a project that might be a good fit for your expertise. Would you be interested in discussing a potential collaboration?",
    status: 'unread',
    priority: 'medium',
    tags: ['collaboration', 'react'],
    createdAt: new Date('2024-01-16T10:30:00Z'),
    updatedAt: new Date('2024-01-16T10:30:00Z'),
    metadata: {
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      ipAddress: '*************',
      referrer: 'https://google.com',
      source: 'contact_form',
    },
  },
  {
    id: '2',
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    subject: 'Speaking Opportunity',
    message:
      "Hello! I'm organizing a tech conference and would love to have you as a speaker. Your blog posts on scalable React applications are exactly what our audience would benefit from. Are you available for speaking engagements?",
    status: 'read',
    priority: 'high',
    tags: ['speaking', 'conference'],
    createdAt: new Date('2024-01-15T14:20:00Z'),
    updatedAt: new Date('2024-01-15T16:45:00Z'),
    metadata: {
      userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      ipAddress: '************',
      referrer: 'https://linkedin.com',
      source: 'contact_form',
    },
  },
  {
    id: '3',
    name: 'Mike Chen',
    email: '<EMAIL>',
    subject: 'Technical Consultation',
    message:
      "We're a early-stage startup building a SaaS platform and need guidance on our technical architecture. Your expertise in microservices would be invaluable. Could we schedule a consultation call?",
    status: 'replied',
    priority: 'medium',
    tags: ['consultation', 'architecture'],
    createdAt: new Date('2024-01-14T09:15:00Z'),
    updatedAt: new Date('2024-01-14T11:30:00Z'),
    reply: {
      message:
        "Hi Mike, thank you for reaching out. I'd be happy to discuss your technical architecture needs. I have availability next week for a consultation call. Please let me know what times work best for you.",
      sentAt: new Date('2024-01-14T11:30:00Z'),
    },
    metadata: {
      userAgent: 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36',
      ipAddress: '*************',
      referrer: 'https://twitter.com',
      source: 'contact_form',
    },
  },
  {
    id: '4',
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    subject: 'Design-Development Partnership',
    message:
      "I'm a UX designer and I've been following your work. I think there could be great synergy between my design skills and your development expertise. Would you be open to exploring partnership opportunities?",
    status: 'archived',
    priority: 'low',
    tags: ['partnership', 'design'],
    createdAt: new Date('2024-01-13T16:45:00Z'),
    updatedAt: new Date('2024-01-13T17:20:00Z'),
    metadata: {
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15',
      ipAddress: '************',
      referrer: 'https://dribbble.com',
      source: 'contact_form',
    },
  },
];

// Get all messages with filtering and pagination
router.get(
  '/',
  asyncHandler(async (req: any, res: any) => {
    const {
      page = 1,
      limit = 10,
      status,
      priority,
      search,
      startDate,
      endDate,
      sort = 'createdAt',
    } = req.query;

    let filteredMessages = [...mockMessages];

    // Apply filters
    if (status) {
      filteredMessages = filteredMessages.filter(msg => msg.status === status);
    }

    if (priority) {
      filteredMessages = filteredMessages.filter(msg => msg.priority === priority);
    }

    if (search) {
      const searchLower = search.toLowerCase();
      filteredMessages = filteredMessages.filter(
        msg =>
          msg.name.toLowerCase().includes(searchLower) ||
          msg.email.toLowerCase().includes(searchLower) ||
          msg.subject.toLowerCase().includes(searchLower) ||
          msg.message.toLowerCase().includes(searchLower)
      );
    }

    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      filteredMessages = filteredMessages.filter(msg => {
        const msgDate = new Date(msg.createdAt);
        return msgDate >= start && msgDate <= end;
      });
    }

    // Apply sorting
    filteredMessages.sort((a, b) => {
      switch (sort) {
        case 'createdAt':
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
        case 'updatedAt':
          return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime();
        case 'name':
          return a.name.localeCompare(b.name);
        case 'subject':
          return a.subject.localeCompare(b.subject);
        case 'priority':
          const priorityOrder = { high: 3, medium: 2, low: 1 };
          return priorityOrder[b.priority] - priorityOrder[a.priority];
        default:
          return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      }
    });

    // Apply pagination
    const skip = (page - 1) * limit;
    const paginatedMessages = filteredMessages.slice(skip, skip + parseInt(limit));
    const total = filteredMessages.length;
    const totalPages = Math.ceil(total / limit);

    res.json({
      success: true,
      data: {
        messages: paginatedMessages,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        },
        stats: {
          unread: mockMessages.filter(m => m.status === 'unread').length,
          read: mockMessages.filter(m => m.status === 'read').length,
          replied: mockMessages.filter(m => m.status === 'replied').length,
          archived: mockMessages.filter(m => m.status === 'archived').length,
        },
      },
    });
  })
);

// Get single message by ID
router.get(
  '/:id',
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;

    const message = mockMessages.find(msg => msg.id === id);

    if (!message) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    res.json({
      success: true,
      data: { message },
    });
  })
);

// Update message status
router.put(
  '/:id/status',
  validateBody({
    status: 'string',
  }),
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    const { status } = req.body;

    const messageIndex = mockMessages.findIndex(msg => msg.id === id);

    if (messageIndex === -1) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    // Validate status
    const validStatuses = ['unread', 'read', 'replied', 'archived'];
    if (!validStatuses.includes(status)) {
      throw createError('Invalid status', HTTP_STATUS.BAD_REQUEST);
    }

    mockMessages[messageIndex].status = status;
    mockMessages[messageIndex].updatedAt = new Date();

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.UPDATED,
      data: { message: mockMessages[messageIndex] },
    });
  })
);

// Reply to message
router.post(
  '/:id/reply',
  validateBody({
    reply: 'string',
    sendEmail: 'boolean?',
  }),
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    const { reply, sendEmail = true } = req.body;

    const messageIndex = mockMessages.findIndex(msg => msg.id === id);

    if (messageIndex === -1) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    // Update message with reply
    mockMessages[messageIndex].reply = {
      message: reply,
      sentAt: new Date(),
    };
    mockMessages[messageIndex].status = 'replied';
    mockMessages[messageIndex].updatedAt = new Date();

    // In production, this would send an actual email
    if (sendEmail) {
      console.log(`Email sent to ${mockMessages[messageIndex].email}: ${reply}`);
    }

    res.json({
      success: true,
      message: 'Reply sent successfully',
      data: { message: mockMessages[messageIndex] },
    });
  })
);

// Add tags to message
router.post(
  '/:id/tags',
  validateBody({
    tags: 'array',
  }),
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    const { tags } = req.body;

    const messageIndex = mockMessages.findIndex(msg => msg.id === id);

    if (messageIndex === -1) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    // Add new tags (avoid duplicates)
    const existingTags = mockMessages[messageIndex].tags || [];
    const newTags = [...new Set([...existingTags, ...tags])];

    mockMessages[messageIndex].tags = newTags;
    mockMessages[messageIndex].updatedAt = new Date();

    res.json({
      success: true,
      message: 'Tags updated successfully',
      data: { message: mockMessages[messageIndex] },
    });
  })
);

// Update message priority
router.put(
  '/:id/priority',
  validateBody({
    priority: 'string',
  }),
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;
    const { priority } = req.body;

    const messageIndex = mockMessages.findIndex(msg => msg.id === id);

    if (messageIndex === -1) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    // Validate priority
    const validPriorities = ['low', 'medium', 'high'];
    if (!validPriorities.includes(priority)) {
      throw createError('Invalid priority', HTTP_STATUS.BAD_REQUEST);
    }

    mockMessages[messageIndex].priority = priority;
    mockMessages[messageIndex].updatedAt = new Date();

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.UPDATED,
      data: { message: mockMessages[messageIndex] },
    });
  })
);

// Delete message
router.delete(
  '/:id',
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;

    const messageIndex = mockMessages.findIndex(msg => msg.id === id);

    if (messageIndex === -1) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    mockMessages.splice(messageIndex, 1);

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.DELETED,
    });
  })
);

// Get AI-suggested replies
router.get(
  '/:id/ai-suggestions',
  asyncHandler(async (req: any, res: any) => {
    const { id } = req.params;

    const message = mockMessages.find(msg => msg.id === id);

    if (!message) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    // Mock AI suggestions based on message content
    const suggestions = [
      {
        type: 'professional',
        message: `Hi ${message.name}, thank you for reaching out. I appreciate your interest and would be happy to discuss this further. Let me review the details and get back to you within 24 hours.`,
        confidence: 0.92,
      },
      {
        type: 'friendly',
        message: `Hello ${message.name}! Thanks for your message. This sounds like an interesting opportunity. I'd love to learn more about what you have in mind. When would be a good time to chat?`,
        confidence: 0.88,
      },
      {
        type: 'brief',
        message: `Hi ${message.name}, thanks for reaching out. I'm interested in learning more. Could you share additional details about the project?`,
        confidence: 0.85,
      },
    ];

    res.json({
      success: true,
      data: { suggestions },
    });
  })
);

export default router;
