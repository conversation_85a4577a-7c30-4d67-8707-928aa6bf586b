const fetch = require('node-fetch');

const ADMIN_BASE_URL = 'http://localhost:3003';

async function testSessionPersistence() {
  console.log('🔧 Testing Session Persistence Issues...\n');

  try {
    // Step 1: Login and get session cookies
    console.log('1. Performing login...');
    const csrfResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/csrf`);
    const csrfData = await csrfResponse.json();
    
    const loginData = new URLSearchParams({
      email: '<EMAIL>',
      password: 'SuperAdmin123!@#',
      csrfToken: csrfData.csrfToken,
      callbackUrl: `${ADMIN_BASE_URL}/admin`,
      json: 'true'
    });

    const loginResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/callback/credentials`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': csrfResponse.headers.get('set-cookie') || '',
      },
      body: loginData.toString(),
    });

    const loginCookies = loginResponse.headers.get('set-cookie') || '';
    console.log('✅ Login successful, cookies obtained');

    // Step 2: Test session endpoint
    console.log('\n2. Testing session endpoint...');
    const sessionResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/session`, {
      headers: { 'Cookie': loginCookies },
    });
    const sessionData = await sessionResponse.json();
    console.log('📊 Session Data:', sessionData);

    if (!sessionData.user) {
      console.log('❌ Session data is empty - this is the core issue!');
      return;
    }

    // Step 3: Test API proxy endpoints with session
    console.log('\n3. Testing API proxy endpoints...');
    
    // Test users stats endpoint
    const statsResponse = await fetch(`${ADMIN_BASE_URL}/api/users/stats/overview`, {
      headers: { 'Cookie': loginCookies },
    });
    console.log('📊 Stats API Status:', statsResponse.status);
    
    if (statsResponse.status === 401) {
      console.log('❌ API proxy returning 401 - authentication headers not being passed correctly');
    } else if (statsResponse.ok) {
      const statsData = await statsResponse.json();
      console.log('✅ Stats API working:', statsData);
    }

    // Test users list endpoint
    const usersResponse = await fetch(`${ADMIN_BASE_URL}/api/users`, {
      headers: { 'Cookie': loginCookies },
    });
    console.log('📊 Users API Status:', usersResponse.status);
    
    if (usersResponse.status === 401) {
      console.log('❌ Users API returning 401 - session not being passed to API proxy');
    } else if (usersResponse.ok) {
      const usersData = await usersResponse.json();
      console.log('✅ Users API working:', usersData);
    }

    // Step 4: Test navigation between pages
    console.log('\n4. Testing page navigation...');
    
    // Test admin dashboard page
    const dashboardResponse = await fetch(`${ADMIN_BASE_URL}/admin`, {
      headers: { 'Cookie': loginCookies },
      redirect: 'manual',
    });
    console.log('📊 Dashboard Page Status:', dashboardResponse.status);
    
    if (dashboardResponse.status === 302) {
      console.log('❌ Dashboard redirecting - session not persisting across page navigation');
      console.log('📊 Redirect Location:', dashboardResponse.headers.get('location'));
    } else {
      console.log('✅ Dashboard accessible');
    }

    // Test users page
    const usersPageResponse = await fetch(`${ADMIN_BASE_URL}/admin/users`, {
      headers: { 'Cookie': loginCookies },
      redirect: 'manual',
    });
    console.log('📊 Users Page Status:', usersPageResponse.status);

    // Step 5: Test session after some time
    console.log('\n5. Testing session persistence after delay...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const delayedSessionResponse = await fetch(`${ADMIN_BASE_URL}/api/auth/session`, {
      headers: { 'Cookie': loginCookies },
    });
    const delayedSessionData = await delayedSessionResponse.json();
    console.log('📊 Delayed Session Data:', delayedSessionData);

    // Step 6: Test direct API calls (bypassing proxy)
    console.log('\n6. Testing direct API calls...');
    
    if (sessionData.accessToken) {
      const directApiResponse = await fetch('http://localhost:3002/api/users/stats/overview', {
        headers: {
          'Authorization': `Bearer ${sessionData.accessToken}`,
          'Content-Type': 'application/json',
        },
      });
      console.log('📊 Direct API Status:', directApiResponse.status);
      
      if (directApiResponse.ok) {
        const directApiData = await directApiResponse.json();
        console.log('✅ Direct API working:', directApiData);
      } else {
        console.log('❌ Direct API failing - token might be invalid');
      }
    } else {
      console.log('❌ No accessToken in session - this is a major issue');
    }

  } catch (error) {
    console.error('❌ Error during session persistence test:', error.message);
  }
}

async function analyzeSessionIssues() {
  console.log('\n🔍 Session Persistence Analysis:\n');
  
  console.log('Common Issues to Check:');
  console.log('1. Session callback not being triggered properly');
  console.log('2. JWT token not being stored in session');
  console.log('3. API proxy routes not extracting session correctly');
  console.log('4. NextAuth configuration issues');
  console.log('5. Cookie domain/path issues');
  console.log('6. CORS issues between admin and API');
  
  console.log('\nExpected Flow:');
  console.log('Login → JWT Callback → Session Callback → Session with accessToken → API Proxy with Bearer token');
  
  console.log('\nTroubleshooting Steps:');
  console.log('1. Check NextAuth session callback logs');
  console.log('2. Verify JWT token structure');
  console.log('3. Test API proxy authentication header passing');
  console.log('4. Check session storage and retrieval');
}

async function runSessionDiagnostics() {
  console.log('🚀 Starting Session Persistence Diagnostics\n');
  
  await testSessionPersistence();
  await analyzeSessionIssues();
  
  console.log('\n🎉 Session diagnostics completed!');
}

runSessionDiagnostics();
