// AI Service for generating response suggestions
// This is a mock implementation that can be replaced with actual AI services

interface AIResponse {
  response: string;
  confidence: number;
  suggestions: string[];
}

interface AIConfig {
  enabled: boolean;
  provider: 'openai' | 'anthropic' | 'mock';
  apiKey?: string;
  model?: string;
  maxTokens?: number;
  temperature?: number;
}

const aiConfig: AIConfig = {
  enabled: process.env.AI_ENABLED === 'true',
  provider: (process.env.AI_PROVIDER as any) || 'mock',
  apiKey: process.env.OPENAI_API_KEY || process.env.ANTHROPIC_API_KEY,
  model: process.env.AI_MODEL || 'gpt-3.5-turbo',
  maxTokens: parseInt(process.env.AI_MAX_TOKENS || '500'),
  temperature: parseFloat(process.env.AI_TEMPERATURE || '0.7'),
};

// Mock AI responses for different message types
const mockResponses: Record<string, string[]> = {
  general: [
    "Thank you for your interest in my work! I'd be happy to discuss this further. Let me know what specific aspects you'd like to explore.",
    "I appreciate you reaching out. This sounds like an interesting opportunity. Could you provide more details about your requirements?",
    "Thanks for contacting me! I'd love to learn more about your project and how I can help. When would be a good time to discuss this?",
  ],
  project: [
    "Thank you for considering me for your project! I'd be excited to discuss the technical requirements and timeline. Could you share more details about the scope?",
    "This sounds like a fascinating project! I'd love to learn more about your technical stack and project goals. When would be convenient for a detailed discussion?",
    "I'm interested in your project proposal. Could you provide more information about the technical challenges and expected deliverables?",
  ],
  consulting: [
    "Thank you for your interest in my consulting services! I'd be happy to discuss how I can help with your technical challenges. Could you share more about your current situation?",
    "I appreciate you reaching out for consulting. I'd love to understand your specific needs and how my expertise can add value to your project.",
    "Thanks for considering my consulting services! Let's schedule a call to discuss your requirements and how I can best assist you.",
  ],
  speaking: [
    "Thank you for the speaking opportunity! I'd be honored to share my insights with your audience. Could you provide more details about the event and topic preferences?",
    "I'm excited about the possibility of speaking at your event! Could you share more information about the audience, format, and preferred topics?",
    "Thanks for considering me as a speaker! I'd love to contribute to your event. When would be a good time to discuss the details?",
  ],
  media: [
    "Thank you for your media inquiry! I'd be happy to participate in your interview/article. Could you share more details about the focus and timeline?",
    "I appreciate your interest in featuring my work! I'd be glad to provide insights for your publication. What specific topics would you like to cover?",
    "Thanks for reaching out for the media opportunity! I'm available for interviews and would love to contribute to your content.",
  ],
  support: [
    "Thank you for reaching out for technical support! I'd be happy to help troubleshoot the issue. Could you provide more details about the problem you're experiencing?",
    "I appreciate you contacting me for support! Let me help you resolve this technical challenge. Could you share more specifics about the issue?",
    "Thanks for your support request! I'd be glad to assist you with this technical problem. When would be a good time to dive into the details?",
  ],
};

class AIService {
  private isEnabled: boolean;

  constructor() {
    this.isEnabled = aiConfig.enabled && !!aiConfig.apiKey;
  }

  async generateResponse(message: string, type: string = 'general'): Promise<string | null> {
    try {
      if (!this.isEnabled) {
        return this.getMockResponse(type);
      }

      switch (aiConfig.provider) {
        case 'openai':
          return await this.generateOpenAIResponse(message, type);
        case 'anthropic':
          return await this.generateAnthropicResponse(message, type);
        default:
          return this.getMockResponse(type);
      }
    } catch (error) {
      console.error('AI response generation failed:', error);
      return this.getMockResponse(type);
    }
  }

  private async generateOpenAIResponse(message: string, type: string): Promise<string> {
    // This would integrate with OpenAI API
    // For now, return mock response
    console.log('OpenAI integration not implemented yet');
    return this.getMockResponse(type);
  }

  private async generateAnthropicResponse(message: string, type: string): Promise<string> {
    // This would integrate with Anthropic API
    // For now, return mock response
    console.log('Anthropic integration not implemented yet');
    return this.getMockResponse(type);
  }

  private getMockResponse(type: string): string {
    const responses = mockResponses[type] || mockResponses.general;
    const randomIndex = Math.floor(Math.random() * responses.length);
    return responses[randomIndex];
  }

  async analyzeMessage(message: string): Promise<{
    sentiment: 'positive' | 'neutral' | 'negative';
    urgency: 'low' | 'medium' | 'high';
    category: string;
    keywords: string[];
  }> {
    try {
      // Mock analysis - in production, this would use actual AI
      const keywords = this.extractKeywords(message);
      const sentiment = this.analyzeSentiment(message);
      const urgency = this.analyzeUrgency(message);
      const category = this.categorizeMessage(message);

      return {
        sentiment,
        urgency,
        category,
        keywords,
      };
    } catch (error) {
      console.error('Message analysis failed:', error);
      return {
        sentiment: 'neutral',
        urgency: 'medium',
        category: 'general',
        keywords: [],
      };
    }
  }

  private extractKeywords(message: string): string[] {
    // Simple keyword extraction
    const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them']);
    
    const words = message.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3 && !commonWords.has(word));
    
    // Count word frequency
    const wordCount = words.reduce((acc, word) => {
      acc[word] = (acc[word] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // Return top keywords
    return Object.entries(wordCount)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([word]) => word);
  }

  private analyzeSentiment(message: string): 'positive' | 'neutral' | 'negative' {
    const positiveWords = ['great', 'excellent', 'amazing', 'wonderful', 'fantastic', 'love', 'like', 'excited', 'interested', 'impressed'];
    const negativeWords = ['bad', 'terrible', 'awful', 'hate', 'dislike', 'problem', 'issue', 'broken', 'error', 'failed'];
    
    const lowerMessage = message.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerMessage.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerMessage.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private analyzeUrgency(message: string): 'low' | 'medium' | 'high' {
    const urgentWords = ['urgent', 'asap', 'immediately', 'emergency', 'critical', 'deadline', 'rush'];
    const lowerMessage = message.toLowerCase();
    
    const urgentCount = urgentWords.filter(word => lowerMessage.includes(word)).length;
    
    if (urgentCount > 0) return 'high';
    if (lowerMessage.includes('soon') || lowerMessage.includes('quick')) return 'medium';
    return 'low';
  }

  private categorizeMessage(message: string): string {
    const categories = {
      project: ['project', 'development', 'build', 'create', 'website', 'app', 'application'],
      consulting: ['consulting', 'advice', 'consultation', 'guidance', 'help', 'assistance'],
      speaking: ['speaking', 'presentation', 'talk', 'conference', 'event', 'workshop'],
      media: ['interview', 'article', 'blog', 'podcast', 'media', 'press'],
      support: ['support', 'bug', 'issue', 'problem', 'error', 'fix', 'troubleshoot'],
    };
    
    const lowerMessage = message.toLowerCase();
    
    for (const [category, keywords] of Object.entries(categories)) {
      if (keywords.some(keyword => lowerMessage.includes(keyword))) {
        return category;
      }
    }
    
    return 'general';
  }

  getConfig(): AIConfig {
    return { ...aiConfig, apiKey: aiConfig.apiKey ? '***' : undefined };
  }

  isServiceEnabled(): boolean {
    return this.isEnabled;
  }
}

const aiService = new AIService();

export const generateAIResponse = (message: string, type?: string) => 
  aiService.generateResponse(message, type);

export const analyzeMessage = (message: string) => 
  aiService.analyzeMessage(message);

export const getAIConfig = () => aiService.getConfig();

export const isAIEnabled = () => aiService.isServiceEnabled();
