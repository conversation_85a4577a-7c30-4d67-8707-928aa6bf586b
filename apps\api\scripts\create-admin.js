const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3002';

async function createAdminUser() {
  try {
    console.log('🔧 Creating admin user...');

    // Register a new user
    const registerResponse = await fetch(`${API_BASE_URL}/api/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'Admin123!@#',
        confirmPassword: 'Admin123!@#',
      }),
    });

    if (!registerResponse.ok) {
      const errorData = await registerResponse.json();
      console.error('❌ Registration failed:', errorData);
      return;
    }

    const userData = await registerResponse.json();
    console.log('✅ User registered successfully:', userData.data.user.email);

    // Note: In a real application, you would need to manually promote this user to admin
    // by directly updating the database or creating a separate admin promotion endpoint
    console.log('📝 Note: You need to manually promote this user to admin role in the database');
    console.log('   User ID:', userData.data.user.id);
    console.log('   Email:', userData.data.user.email);
    console.log('   Current Role:', userData.data.user.role);
  } catch (error) {
    console.error('❌ Error creating admin user:', error.message);
  }
}

// Run the script
createAdminUser();
