import React from 'react';

interface LoadingSkeletonProps {
  className?: string;
  variant?: 'text' | 'circular' | 'rectangular' | 'rounded';
  width?: string | number;
  height?: string | number;
  animation?: 'pulse' | 'wave' | 'none';
}

export function LoadingSkeleton({
  className = '',
  variant = 'rectangular',
  width,
  height,
  animation = 'pulse'
}: LoadingSkeletonProps) {
  const baseClasses = 'bg-gray-200 animate-pulse';
  
  const variantClasses = {
    text: 'rounded',
    circular: 'rounded-full',
    rectangular: '',
    rounded: 'rounded-lg'
  };

  const animationClasses = {
    pulse: 'animate-pulse',
    wave: 'animate-shimmer',
    none: ''
  };

  const style: React.CSSProperties = {};
  if (width) style.width = typeof width === 'number' ? `${width}px` : width;
  if (height) style.height = typeof height === 'number' ? `${height}px` : height;

  return (
    <div
      className={`${baseClasses} ${variantClasses[variant]} ${animationClasses[animation]} ${className}`}
      style={style}
    />
  );
}

// Dashboard specific loading components
export function DashboardStatsLoading() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
      {Array.from({ length: 5 }).map((_, index) => (
        <div
          key={index}
          className="bg-white rounded-2xl border border-gray-100 p-6 shadow-sm"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <div className="flex items-center justify-between mb-4">
            <LoadingSkeleton variant="circular" width={48} height={48} />
            <LoadingSkeleton variant="rounded" width={60} height={20} />
          </div>
          <div className="space-y-2">
            <LoadingSkeleton variant="text" width="60%" height={16} />
            <LoadingSkeleton variant="text" width="80%" height={32} />
          </div>
          <div className="mt-4 pt-4 border-t border-gray-100">
            <LoadingSkeleton variant="text" width="40%" height={12} />
          </div>
        </div>
      ))}
    </div>
  );
}

export function DashboardContentLoading() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {Array.from({ length: 2 }).map((_, cardIndex) => (
        <div
          key={cardIndex}
          className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden"
        >
          <div className="bg-gray-50 px-6 py-4 border-b border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <LoadingSkeleton variant="circular" width={20} height={20} />
                <LoadingSkeleton variant="text" width={120} height={20} className="ml-2" />
              </div>
              <LoadingSkeleton variant="rounded" width={40} height={16} />
            </div>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, itemIndex) => (
                <div
                  key={itemIndex}
                  className="flex items-center justify-between p-4 rounded-xl border border-gray-100"
                >
                  <div className="flex-1">
                    <LoadingSkeleton variant="text" width="70%" height={16} className="mb-2" />
                    <LoadingSkeleton variant="text" width="40%" height={12} />
                  </div>
                  <div className="flex items-center space-x-2 ml-4">
                    <LoadingSkeleton variant="rounded" width={60} height={24} />
                    <LoadingSkeleton variant="circular" width={16} height={16} />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

export function DashboardAnalyticsLoading() {
  return (
    <div className="bg-white rounded-2xl border border-gray-100 shadow-sm overflow-hidden">
      <div className="bg-gray-50 px-6 py-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <LoadingSkeleton variant="circular" width={20} height={20} />
            <LoadingSkeleton variant="text" width={140} height={20} className="ml-2" />
          </div>
          <div className="flex items-center space-x-2">
            <LoadingSkeleton variant="rounded" width={80} height={16} />
            <LoadingSkeleton variant="circular" width={8} height={8} />
          </div>
        </div>
      </div>
      <div className="p-8">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {Array.from({ length: 4 }).map((_, index) => (
            <div
              key={index}
              className="text-center p-6 rounded-xl bg-gray-50 border border-gray-100"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="flex justify-center mb-4">
                <LoadingSkeleton variant="circular" width={48} height={48} />
              </div>
              <LoadingSkeleton variant="text" width="80%" height={32} className="mb-2 mx-auto" />
              <LoadingSkeleton variant="text" width="60%" height={16} className="mb-3 mx-auto" />
              <LoadingSkeleton variant="text" width="50%" height={12} className="mx-auto" />
              <div className="mt-4">
                <LoadingSkeleton variant="rounded" width="100%" height={6} />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Shimmer animation for wave effect
export function ShimmerEffect() {
  return (
    <style jsx global>{`
      @keyframes shimmer {
        0% {
          background-position: -200px 0;
        }
        100% {
          background-position: calc(200px + 100%) 0;
        }
      }
      
      .animate-shimmer {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200px 100%;
        animation: shimmer 1.5s infinite;
      }
    `}</style>
  );
}
