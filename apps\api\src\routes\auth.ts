import { Router } from 'express';
import jwt from 'jsonwebtoken';
import { User, PermissionTemplate, defaultPermissions } from '@rxy/database';
import { config } from '../config';
import { validateBody } from '../middleware/validation';
import { authenticate, requireRole } from '../middleware/auth';
import { asyncHandler, createError } from '../middleware/errorHandler';
import { emailService } from '../services/emailService';
import { schemas, HTTP_STATUS, SUCCESS_MESSAGES, ERROR_MESSAGES } from '@rxy/shared';

const router = Router();

// Check if registration is allowed
router.get(
  '/registration-status',
  asyncHandler(async (req: any, res: any) => {
    const userCount = await User.countDocuments();
    const isFirstUser = userCount === 0;

    res.json({
      success: true,
      data: {
        registrationOpen: isFirstUser,
        isFirstUser,
        message: isFirstUser
          ? 'Registration is open for the first Super Admin account'
          : 'Registration is closed. Contact an administrator for access.',
      },
    });
  })
);

// Register Super Admin (only if no users exist)
router.post(
  '/register-super-admin',
  validateBody(schemas.register),
  asyncHandler(async (req: any, res: any) => {
    const { firstName, lastName, email, password } = req.body;

    // Check if any users exist
    const userCount = await User.countDocuments();
    if (userCount > 0) {
      throw createError(
        'Registration is closed. Super Admin already exists.',
        HTTP_STATUS.FORBIDDEN
      );
    }

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      throw createError('User already exists with this email', HTTP_STATUS.CONFLICT);
    }

    // Initialize default permissions if they don't exist
    const permissionCount = await PermissionTemplate.countDocuments();
    if (permissionCount === 0) {
      await PermissionTemplate.insertMany(defaultPermissions);
    }

    // Get super admin permissions
    const superAdminPermissions = await PermissionTemplate.getDefaultPermissions('super_admin');

    // Create Super Admin user
    const user = new User({
      firstName,
      lastName,
      name: `${firstName} ${lastName}`,
      email,
      password,
      role: 'super_admin',
      isSuperAdmin: true,
      isFirstUser: true,
      status: 'active',
      permissions: superAdminPermissions.map(p => ({
        resource: p.resource,
        actions: p.actions,
      })),
    });

    // Generate email verification token
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // Send verification email
    await emailService.sendVerificationEmail(email, user.name, verificationToken);

    res.status(HTTP_STATUS.CREATED).json({
      success: true,
      message:
        'Super Admin account created successfully. Please check your email to verify your account.',
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          emailVerified: user.emailVerified,
        },
        requiresEmailVerification: true,
      },
    });
  })
);

// Create Admin User (Super Admin only)
router.post(
  '/create-admin',
  authenticate,
  requireRole('super_admin'),
  validateBody(schemas.register),
  asyncHandler(async (req: any, res: any) => {
    const { firstName, lastName, email, password, role = 'admin', permissions = [] } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);
    if (existingUser) {
      throw createError('User already exists with this email', HTTP_STATUS.CONFLICT);
    }

    // Validate role
    if (!['admin', 'editor'].includes(role)) {
      throw createError(
        'Invalid role. Only admin and editor roles can be created.',
        HTTP_STATUS.BAD_REQUEST
      );
    }

    // Get default permissions for the role
    const defaultPerms = await PermissionTemplate.getDefaultPermissions(role);
    const userPermissions =
      permissions.length > 0
        ? permissions
        : defaultPerms.map(p => ({
            resource: p.resource,
            actions: p.actions,
          }));

    // Create new admin user
    const user = new User({
      firstName,
      lastName,
      name: `${firstName} ${lastName}`,
      email,
      password,
      role,
      permissions: userPermissions,
      createdBy: req.user.id,
      status: 'active',
    });

    // Generate email verification token
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // Send verification email
    await emailService.sendVerificationEmail(email, user.name, verificationToken);

    res.status(HTTP_STATUS.CREATED).json({
      success: true,
      message: `${
        role.charAt(0).toUpperCase() + role.slice(1)
      } account created successfully. Verification email sent.`,
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          permissions: user.permissions,
          emailVerified: user.emailVerified,
        },
      },
    });
  })
);

// Login
router.post(
  '/login',
  validateBody(schemas.login),
  asyncHandler(async (req: any, res: any) => {
    const { email, password } = req.body;

    // Find user by email
    const user = await User.findByEmail(email);
    if (!user) {
      throw createError('Invalid credentials', HTTP_STATUS.UNAUTHORIZED);
    }

    // Check if user has admin access (only super_admin and admin can login to admin panel)
    if (!['super_admin', 'admin', 'editor'].includes(user.role)) {
      throw createError('Access denied. Admin privileges required.', HTTP_STATUS.FORBIDDEN);
    }

    // Check if account is active
    if (user.status !== 'active') {
      throw createError('Account is inactive. Contact administrator.', HTTP_STATUS.FORBIDDEN);
    }

    // Check if email is verified
    if (!user.emailVerified) {
      throw createError(
        'Email not verified. Please check your email and verify your account.',
        HTTP_STATUS.FORBIDDEN
      );
    }

    // Check if account is locked
    if (user.isLocked()) {
      throw createError(
        'Account is temporarily locked due to too many failed login attempts',
        HTTP_STATUS.UNAUTHORIZED
      );
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);
    if (!isPasswordValid) {
      await user.incrementLoginAttempts();
      throw createError('Invalid credentials', HTTP_STATUS.UNAUTHORIZED);
    }

    // Reset login attempts on successful login
    await user.resetLoginAttempts();

    // Update last login
    user.lastLoginAt = new Date();
    await user.save();

    // Generate tokens
    const accessToken = user.generateAccessToken();
    const refreshToken = user.generateRefreshToken();

    // Save refresh token
    await user.addRefreshToken(refreshToken);

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.LOGIN,
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          role: user.role,
          permissions: user.permissions,
          isSuperAdmin: user.isSuperAdmin,
          emailVerified: user.emailVerified,
          lastLoginAt: user.lastLoginAt,
        },
        tokens: {
          accessToken,
          refreshToken,
        },
      },
    });
  })
);

// Verify Email
router.post(
  '/verify-email',
  asyncHandler(async (req: any, res: any) => {
    const { token } = req.body;

    if (!token) {
      throw createError('Verification token is required', HTTP_STATUS.BAD_REQUEST);
    }

    const user = await User.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: new Date() },
    });

    if (!user) {
      throw createError('Invalid or expired verification token', HTTP_STATUS.BAD_REQUEST);
    }

    user.emailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();

    res.json({
      success: true,
      message: 'Email verified successfully. You can now log in.',
      data: {
        user: {
          id: user._id,
          email: user.email,
          name: user.name,
          emailVerified: user.emailVerified,
        },
      },
    });
  })
);

// Resend Verification Email
router.post(
  '/resend-verification',
  asyncHandler(async (req: any, res: any) => {
    const { email } = req.body;

    if (!email) {
      throw createError('Email is required', HTTP_STATUS.BAD_REQUEST);
    }

    const user = await User.findByEmail(email);
    if (!user) {
      throw createError('User not found', HTTP_STATUS.NOT_FOUND);
    }

    if (user.emailVerified) {
      throw createError('Email is already verified', HTTP_STATUS.BAD_REQUEST);
    }

    // Generate new verification token
    const verificationToken = user.generateEmailVerificationToken();
    await user.save();

    // Send verification email
    await emailService.sendVerificationEmail(user.email, user.name, verificationToken);

    res.json({
      success: true,
      message: 'Verification email sent successfully.',
    });
  })
);

// Refresh Token
router.post(
  '/refresh',
  asyncHandler(async (req: any, res: any) => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw createError('Refresh token is required', HTTP_STATUS.BAD_REQUEST);
    }

    try {
      // Verify refresh token
      const decoded = jwt.verify(refreshToken, config.JWT_REFRESH_SECRET) as any;

      // Find user and check if refresh token exists
      const user = await User.findById(decoded.id);
      if (!user || !user.refreshTokens.includes(refreshToken)) {
        throw createError('Invalid refresh token', HTTP_STATUS.UNAUTHORIZED);
      }

      // Generate new tokens
      const newAccessToken = user.generateAccessToken();
      const newRefreshToken = user.generateRefreshToken();

      // Replace old refresh token with new one
      await user.removeRefreshToken(refreshToken);
      await user.addRefreshToken(newRefreshToken);

      res.json({
        success: true,
        data: {
          tokens: {
            accessToken: newAccessToken,
            refreshToken: newRefreshToken,
          },
        },
      });
    } catch (error) {
      throw createError('Invalid refresh token', HTTP_STATUS.UNAUTHORIZED);
    }
  })
);

// Logout
router.post(
  '/logout',
  authenticate,
  asyncHandler(async (req: any, res: any) => {
    const { refreshToken } = req.body;
    const userId = req.user.id;

    const user = await User.findById(userId);
    if (user && refreshToken) {
      await user.removeRefreshToken(refreshToken);
    }

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.LOGOUT,
    });
  })
);

// Logout All (clear all refresh tokens)
router.post(
  '/logout-all',
  authenticate,
  asyncHandler(async (req: any, res: any) => {
    const userId = req.user.id;

    const user = await User.findById(userId);
    if (user) {
      await user.clearRefreshTokens();
    }

    res.json({
      success: true,
      message: 'Logged out from all devices',
    });
  })
);

// Get Current User
router.get(
  '/me',
  authenticate,
  asyncHandler(async (req: any, res: any) => {
    const user = await User.findById(req.user.id);
    if (!user) {
      throw createError(ERROR_MESSAGES.NOT_FOUND, HTTP_STATUS.NOT_FOUND);
    }

    res.json({
      success: true,
      data: { user: user.toJSON() },
    });
  })
);

// Forgot Password
router.post(
  '/forgot-password',
  validateBody(schemas.forgotPassword),
  asyncHandler(async (req: any, res: any) => {
    const { email } = req.body;

    const user = await User.findByEmail(email);
    if (!user) {
      // Don't reveal if email exists or not
      res.json({
        success: true,
        message: SUCCESS_MESSAGES.PASSWORD_RESET,
      });
      return;
    }

    // Generate reset token
    const resetToken = generateId(32);
    user.passwordResetToken = resetToken;
    user.passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes
    await user.save();

    // TODO: Send email with reset link
    // await sendPasswordResetEmail(user.email, resetToken);

    res.json({
      success: true,
      message: SUCCESS_MESSAGES.PASSWORD_RESET,
    });
  })
);

// Reset Password
router.post(
  '/reset-password',
  validateBody(schemas.resetPassword),
  asyncHandler(async (req: any, res: any) => {
    const { token, password } = req.body;

    const user = await User.findByResetToken(token);
    if (!user) {
      throw createError('Invalid or expired reset token', HTTP_STATUS.BAD_REQUEST);
    }

    // Update password and clear reset token
    user.password = password;
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;

    // Clear all refresh tokens for security
    await user.clearRefreshTokens();

    await user.save();

    res.json({
      success: true,
      message: 'Password reset successfully',
    });
  })
);

export default router;
