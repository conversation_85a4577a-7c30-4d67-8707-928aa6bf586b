import mongoose, { Document, Schema } from 'mongoose';

export interface IMessage extends Document {
  name: string;
  email: string;
  subject: string;
  message: string;
  type: 'general' | 'project' | 'consulting' | 'speaking' | 'media' | 'support';
  status: 'unread' | 'read' | 'replied' | 'archived';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  source: 'contact_form' | 'email' | 'social_media' | 'referral';
  tags: string[];
  assignedTo?: mongoose.Types.ObjectId;
  replies: {
    content: string;
    author: mongoose.Types.ObjectId;
    sentAt: Date;
    method: 'email' | 'phone' | 'meeting';
  }[];
  metadata: {
    userAgent?: string;
    ip?: string;
    referrer?: string;
    timestamp: Date;
    location?: {
      country?: string;
      city?: string;
      timezone?: string;
    };
  };
  aiSuggestion?: {
    response: string;
    confidence: number;
    generatedAt: Date;
  };
  followUp?: {
    scheduled: boolean;
    date?: Date;
    notes?: string;
  };
  createdAt: Date;
  updatedAt: Date;
  readAt?: Date;
  repliedAt?: Date;
}

const MessageSchema = new Schema<IMessage>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100,
    },
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
    },
    subject: {
      type: String,
      required: true,
      trim: true,
      maxlength: 200,
    },
    message: {
      type: String,
      required: true,
      trim: true,
      maxlength: 5000,
    },
    type: {
      type: String,
      enum: ['general', 'project', 'consulting', 'speaking', 'media', 'support'],
      default: 'general',
    },
    status: {
      type: String,
      enum: ['unread', 'read', 'replied', 'archived'],
      default: 'unread',
    },
    priority: {
      type: String,
      enum: ['low', 'medium', 'high', 'urgent'],
      default: 'medium',
    },
    source: {
      type: String,
      enum: ['contact_form', 'email', 'social_media', 'referral'],
      default: 'contact_form',
    },
    tags: [{
      type: String,
      trim: true,
      lowercase: true,
    }],
    assignedTo: {
      type: Schema.Types.ObjectId,
      ref: 'User',
    },
    replies: [{
      content: {
        type: String,
        required: true,
        trim: true,
      },
      author: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
      },
      sentAt: {
        type: Date,
        default: Date.now,
      },
      method: {
        type: String,
        enum: ['email', 'phone', 'meeting'],
        default: 'email',
      },
    }],
    metadata: {
      userAgent: String,
      ip: String,
      referrer: String,
      timestamp: {
        type: Date,
        default: Date.now,
      },
      location: {
        country: String,
        city: String,
        timezone: String,
      },
    },
    aiSuggestion: {
      response: String,
      confidence: {
        type: Number,
        min: 0,
        max: 1,
      },
      generatedAt: Date,
    },
    followUp: {
      scheduled: {
        type: Boolean,
        default: false,
      },
      date: Date,
      notes: String,
    },
    readAt: Date,
    repliedAt: Date,
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes
MessageSchema.index({ status: 1, createdAt: -1 });
MessageSchema.index({ type: 1, status: 1 });
MessageSchema.index({ priority: 1, status: 1 });
MessageSchema.index({ email: 1 });
MessageSchema.index({ assignedTo: 1, status: 1 });
MessageSchema.index({ tags: 1 });
MessageSchema.index({ 'metadata.timestamp': -1 });

// Virtual for response time
MessageSchema.virtual('responseTime').get(function() {
  if (this.repliedAt && this.createdAt) {
    return this.repliedAt.getTime() - this.createdAt.getTime();
  }
  return null;
});

// Virtual for age
MessageSchema.virtual('age').get(function() {
  return Date.now() - this.createdAt.getTime();
});

// Pre-save middleware
MessageSchema.pre('save', function(next) {
  if (this.isModified('status')) {
    if (this.status === 'read' && !this.readAt) {
      this.readAt = new Date();
    }
    if (this.status === 'replied' && !this.repliedAt) {
      this.repliedAt = new Date();
    }
  }
  next();
});

// Static methods
MessageSchema.statics.findUnread = function() {
  return this.find({ status: 'unread' }).sort({ createdAt: -1 });
};

MessageSchema.statics.findByType = function(type: string) {
  return this.find({ type }).sort({ createdAt: -1 });
};

MessageSchema.statics.findByPriority = function(priority: string) {
  return this.find({ priority }).sort({ createdAt: -1 });
};

MessageSchema.statics.getStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
      },
    },
  ]);
};

export const Message = mongoose.model<IMessage>('Message', MessageSchema);
