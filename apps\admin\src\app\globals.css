@tailwind base;
@tailwind components;
@tailwind utilities;

/* Professional Design System */
:root {
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-500: #3b82f6;
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;

  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;

  --color-accent-50: #fef2f2;
  --color-accent-100: #fee2e2;
  --color-accent-500: #ef4444;
  --color-accent-600: #dc2626;
  --color-accent-700: #b91c1c;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;

  --color-secondary-50: #f8fafc;
  --color-secondary-100: #f1f5f9;
  --color-secondary-500: #64748b;
  --color-secondary-600: #475569;
  --color-secondary-700: #334155;

  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  letter-spacing: -0.025em;
}

/* Professional Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.4s ease-out;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Component styles */
.admin-sidebar {
  width: 16rem;
  border-right: 1px solid #e5e7eb;
  background-color: #ffffff;
}

.admin-main {
  flex: 1;
  overflow: auto;
}

.admin-header {
  height: 4rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
}

.admin-content {
  padding: 1.5rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.625rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.published {
  background-color: rgb(220 252 231);
  color: rgb(22 101 52);
}

.status-badge.draft {
  background-color: rgb(254 249 195);
  color: rgb(133 77 14);
}

.status-badge.archived {
  background-color: rgb(243 244 246);
  color: rgb(55 65 81);
}

/* Professional Card Styles */
.card {
  background: white;
  border-radius: 1rem;
  border: 1px solid var(--color-gray-100);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  border-color: var(--color-gray-200);
  transform: translateY(-2px);
}

.card-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--color-gray-100);
  background: linear-gradient(135deg, var(--color-gray-50) 0%, white 100%);
  border-radius: 1rem 1rem 0 0;
}

.card-content {
  padding: 1.5rem;
}

/* Professional Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.75rem;
  font-weight: 500;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
}

.btn:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.btn-primary {
  background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
  color: white;
  padding: 0.75rem 1.5rem;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--color-primary-600), var(--color-primary-700));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.btn-secondary {
  background: white;
  color: var(--color-gray-700);
  border: 1px solid var(--color-gray-300);
  padding: 0.75rem 1.5rem;
}

.btn-secondary:hover {
  background: var(--color-gray-50);
  border-color: var(--color-gray-400);
  transform: translateY(-1px);
}

/* Professional Input Styles */
.input {
  display: block;
  width: 100%;
  border-radius: 0.75rem;
  border: 1px solid var(--color-gray-300);
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  transition: all 0.2s ease;
  background: white;
}

.input:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input:hover {
  border-color: var(--color-gray-400);
}

/* Professional Table Styles */
.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.table th {
  background: var(--color-gray-50);
  padding: 1rem;
  text-align: left;
  font-weight: 600;
  color: var(--color-gray-700);
  border-bottom: 1px solid var(--color-gray-200);
}

.table td {
  padding: 1rem;
  border-bottom: 1px solid var(--color-gray-100);
  color: var(--color-gray-900);
}

.table tr:hover {
  background: var(--color-gray-50);
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: -100%;
    height: 100vh;
    z-index: 50;
    transition: left 0.3s ease;
  }

  .admin-sidebar.open {
    left: 0;
  }

  .admin-content {
    padding: 1rem;
  }

  .card {
    border-radius: 0.75rem;
  }

  .card-header,
  .card-content {
    padding: 1rem;
  }
}
