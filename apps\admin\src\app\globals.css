@tailwind base;
@tailwind components;
@tailwind utilities;

/* Tailwind-compatible Design System */
@layer base {
  :root {
    /* Tailwind HSL Variables */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  letter-spacing: -0.025em;
}

/* Professional Animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0%,
  100% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.4s ease-out;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* Component styles */
.admin-sidebar {
  width: 16rem;
  border-right: 1px solid #e5e7eb;
  background-color: #ffffff;
}

.admin-main {
  flex: 1;
  overflow: auto;
}

.admin-header {
  height: 4rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
}

.admin-content {
  padding: 1.5rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.625rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.published {
  background-color: rgb(220 252 231);
  color: rgb(22 101 52);
}

.status-badge.draft {
  background-color: rgb(254 249 195);
  color: rgb(133 77 14);
}

.status-badge.archived {
  background-color: rgb(243 244 246);
  color: rgb(55 65 81);
}

/* Professional Component Styles */
@layer components {
  .professional-card {
    @apply bg-white rounded-2xl border border-gray-100 shadow-sm transition-all duration-300 hover:shadow-xl hover:border-gray-200 hover:-translate-y-1;
  }

  .professional-card-header {
    @apply p-6 border-b border-gray-100 bg-gradient-to-r from-gray-50 to-white rounded-t-2xl;
  }

  .professional-card-content {
    @apply p-6;
  }

  .professional-btn {
    @apply inline-flex items-center justify-center rounded-xl font-medium text-sm transition-all duration-200 cursor-pointer border-0 no-underline;
  }

  .professional-btn:focus {
    @apply outline-none ring-2 ring-blue-500/20;
  }

  .professional-btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-blue-600 text-white px-6 py-3 hover:from-blue-600 hover:to-blue-700 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-500/25;
  }

  .professional-btn-secondary {
    @apply bg-white text-gray-700 border border-gray-300 px-6 py-3 hover:bg-gray-50 hover:border-gray-400 hover:-translate-y-0.5;
  }

  .professional-input {
    @apply block w-full rounded-xl border border-gray-300 px-4 py-3 text-sm transition-all duration-200 bg-white hover:border-gray-400 focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20;
  }

  .professional-table {
    @apply w-full border-collapse bg-white rounded-2xl overflow-hidden shadow-sm;
  }

  .professional-table th {
    @apply bg-gray-50 px-6 py-4 text-left font-semibold text-gray-700 border-b border-gray-200;
  }

  .professional-table td {
    @apply px-6 py-4 border-b border-gray-100 text-gray-900;
  }

  .professional-table tr:hover {
    @apply bg-gray-50;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: -100%;
    height: 100vh;
    z-index: 50;
    transition: left 0.3s ease;
  }

  .admin-sidebar.open {
    left: 0;
  }

  .admin-content {
    padding: 1rem;
  }

  .card {
    border-radius: 0.75rem;
  }

  .card-header,
  .card-content {
    padding: 1rem;
  }
}
