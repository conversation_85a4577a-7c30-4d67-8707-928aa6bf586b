@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    border-color: hsl(var(--border));
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: 'rlig' 1, 'calt' 1;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
  }
}

@layer components {
  .admin-sidebar {
    @apply w-64 bg-card border-r;
    border-color: hsl(var(--border));
  }

  .admin-main {
    @apply flex-1 overflow-auto;
  }

  .admin-header {
    @apply h-16 border-b bg-background/95 backdrop-blur;
    border-color: hsl(var(--border));
  }

  .admin-content {
    @apply p-6;
  }

  .status-badge {
    @apply inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium;
  }

  .status-badge.published {
    @apply bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400;
  }

  .status-badge.draft {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400;
  }

  .status-badge.archived {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400;
  }
}
