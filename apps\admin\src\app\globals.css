@tailwind base;
@tailwind components;
@tailwind utilities;

h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: 600;
  letter-spacing: -0.025em;
}

/* Component styles */
.admin-sidebar {
  width: 16rem;
  border-right: 1px solid #e5e7eb;
  background-color: #ffffff;
}

.admin-main {
  flex: 1;
  overflow: auto;
}

.admin-header {
  height: 4rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
}

.admin-content {
  padding: 1.5rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  border-radius: 9999px;
  padding: 0.125rem 0.625rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.published {
  background-color: rgb(220 252 231);
  color: rgb(22 101 52);
}

.status-badge.draft {
  background-color: rgb(254 249 195);
  color: rgb(133 77 14);
}

.status-badge.archived {
  background-color: rgb(243 244 246);
  color: rgb(55 65 81);
}
