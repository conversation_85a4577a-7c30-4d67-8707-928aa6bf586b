import { Router } from 'express';
import { Project } from '../models/Project';
import { authenticate, requireRole } from '../middleware/auth';
import { validateBody } from '../middleware/validation';
import { projectSchema, updateProjectSchema } from '../schemas/project';
import { ApiResponse } from '@rxy/shared';

const router = Router();

// GET /api/projects - Get all published projects
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, category, featured } = req.query;

    const filter: any = { status: 'published' };

    if (category) {
      filter.category = category;
    }

    if (featured === 'true') {
      filter.featured = true;
    }

    const skip = (Number(page) - 1) * Number(limit);

    const [projects, total] = await Promise.all([
      Project.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(Number(limit))
        .populate('author', 'name email')
        .lean(),
      Project.countDocuments(filter),
    ]);

    const response: ApiResponse<any> = {
      success: true,
      data: {
        projects,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total,
          pages: Math.ceil(total / Number(limit)),
        },
      },
    };

    res.json(response);
  } catch (error) {
    console.error('Error fetching projects:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch projects',
    });
  }
});

// GET /api/projects/:slug - Get project by slug
router.get('/:slug', async (req, res) => {
  try {
    const { slug } = req.params;

    const project = await Project.findOne({
      slug,
      status: 'published',
    })
      .populate('author', 'name email')
      .lean();

    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found',
      });
    }

    // Increment view count
    await Project.findByIdAndUpdate(project._id, {
      $inc: { views: 1 },
    });

    const response: ApiResponse<any> = {
      success: true,
      data: { ...project, views: project.views + 1 },
    };

    res.json(response);
  } catch (error) {
    console.error('Error fetching project:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch project',
    });
  }
});

// GET /api/projects/categories - Get all project categories
router.get('/meta/categories', async (req, res) => {
  try {
    const categories = await Project.distinct('category', { status: 'published' });

    const response: ApiResponse<string[]> = {
      success: true,
      data: categories,
    };

    res.json(response);
  } catch (error) {
    console.error('Error fetching categories:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch categories',
    });
  }
});

// GET /api/projects/featured - Get featured projects
router.get('/meta/featured', async (req, res) => {
  try {
    const projects = await Project.find({
      status: 'published',
      featured: true,
    })
      .sort({ createdAt: -1 })
      .limit(6)
      .populate('author', 'name email')
      .lean();

    const response: ApiResponse<any[]> = {
      success: true,
      data: projects,
    };

    res.json(response);
  } catch (error) {
    console.error('Error fetching featured projects:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch featured projects',
    });
  }
});

// POST /api/projects - Create new project (admin only)
router.post(
  '/',
  authenticate,
  requireRole(['super_admin', 'admin']),
  validateBody(projectSchema),
  async (req, res) => {
    try {
      const projectData = {
        ...req.body,
        author: req.user.id,
        slug: req.body.slug || req.body.title.toLowerCase().replace(/[^a-z0-9]+/g, '-'),
      };

      const project = new Project(projectData);
      await project.save();

      const response: ApiResponse<any> = {
        success: true,
        data: project,
        message: 'Project created successfully',
      };

      res.status(201).json(response);
    } catch (error) {
      console.error('Error creating project:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to create project',
      });
    }
  }
);

// PUT /api/projects/:id - Update project (admin only)
router.put(
  '/:id',
  authenticate,
  requireRole(['super_admin', 'admin']),
  validateBody(updateProjectSchema),
  async (req, res) => {
    try {
      const { id } = req.params;

      const project = await Project.findByIdAndUpdate(
        id,
        { ...req.body, updatedAt: new Date() },
        { new: true, runValidators: true }
      );

      if (!project) {
        return res.status(404).json({
          success: false,
          error: 'Project not found',
        });
      }

      const response: ApiResponse<any> = {
        success: true,
        data: project,
        message: 'Project updated successfully',
      };

      res.json(response);
    } catch (error) {
      console.error('Error updating project:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to update project',
      });
    }
  }
);

// DELETE /api/projects/:id - Delete project (admin only)
router.delete('/:id', authenticate, requireRole(['super_admin', 'admin']), async (req, res) => {
  try {
    const { id } = req.params;

    const project = await Project.findByIdAndDelete(id);

    if (!project) {
      return res.status(404).json({
        success: false,
        error: 'Project not found',
      });
    }

    const response: ApiResponse<null> = {
      success: true,
      data: null,
      message: 'Project deleted successfully',
    };

    res.json(response);
  } catch (error) {
    console.error('Error deleting project:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete project',
    });
  }
});

export default router;
