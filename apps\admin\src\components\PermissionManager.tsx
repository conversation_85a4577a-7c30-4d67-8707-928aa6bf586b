'use client';

import { useState, useEffect } from 'react';
import {
  ShieldCheckIcon,
  PencilIcon,
  CheckIcon,
  XMarkIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  LockClosedIcon,
  LockOpenIcon,
} from '@heroicons/react/24/outline';

interface Permission {
  resource: string;
  actions: string[];
}

interface PermissionManagerProps {
  userId: string;
  userRole: string;
  currentPermissions: Permission[];
  onPermissionsUpdate: (permissions: Permission[]) => void;
  isEditable?: boolean;
}

// Available resources and their possible actions
const AVAILABLE_RESOURCES = {
  users: {
    name: 'User Management',
    description: 'Manage user accounts and permissions',
    icon: '👥',
    actions: ['read', 'write', 'delete', 'manage'],
  },
  blog: {
    name: 'Blog Management',
    description: 'Create and manage blog posts',
    icon: '📝',
    actions: ['read', 'write', 'delete', 'publish', 'moderate'],
  },
  projects: {
    name: 'Project Management',
    description: 'Manage portfolio projects',
    icon: '🚀',
    actions: ['read', 'write', 'delete', 'publish'],
  },
  messages: {
    name: 'Message Management',
    description: 'Handle contact forms and messages',
    icon: '💬',
    actions: ['read', 'write', 'moderate'],
  },
  analytics: {
    name: 'Analytics',
    description: 'View site analytics and reports',
    icon: '📊',
    actions: ['read', 'export', 'manage'],
  },
  settings: {
    name: 'System Settings',
    description: 'Configure system settings',
    icon: '⚙️',
    actions: ['read', 'write', 'manage'],
  },
  media: {
    name: 'Media Management',
    description: 'Upload and manage media files',
    icon: '🖼️',
    actions: ['read', 'write', 'delete'],
  },
  comments: {
    name: 'Comment Moderation',
    description: 'Moderate blog comments',
    icon: '💭',
    actions: ['read', 'moderate'],
  },
  newsletter: {
    name: 'Newsletter',
    description: 'Manage newsletter campaigns',
    icon: '📧',
    actions: ['read', 'write', 'manage'],
  },
};

const ACTION_DESCRIPTIONS = {
  read: 'View and access content',
  write: 'Create and edit content',
  delete: 'Remove content permanently',
  publish: 'Make content public',
  moderate: 'Review and approve content',
  manage: 'Full administrative control',
  export: 'Export data and reports',
  import: 'Import data from external sources',
};

const ACTION_COLORS = {
  read: 'bg-blue-100 text-blue-800 border-blue-200',
  write: 'bg-green-100 text-green-800 border-green-200',
  delete: 'bg-red-100 text-red-800 border-red-200',
  publish: 'bg-purple-100 text-purple-800 border-purple-200',
  moderate: 'bg-yellow-100 text-yellow-800 border-yellow-200',
  manage: 'bg-indigo-100 text-indigo-800 border-indigo-200',
  export: 'bg-gray-100 text-gray-800 border-gray-200',
  import: 'bg-cyan-100 text-cyan-800 border-cyan-200',
};

export default function PermissionManager({
  userId,
  userRole,
  currentPermissions,
  onPermissionsUpdate,
  isEditable = true,
}: PermissionManagerProps) {
  const [permissions, setPermissions] = useState<Permission[]>(currentPermissions);
  const [isEditing, setIsEditing] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  useEffect(() => {
    setPermissions(currentPermissions);
  }, [currentPermissions]);

  const hasPermission = (resource: string, action: string): boolean => {
    const permission = permissions.find(p => p.resource === resource);
    return permission?.actions.includes(action) || false;
  };

  const togglePermission = (resource: string, action: string) => {
    if (!isEditing) return;

    const newPermissions = [...permissions];
    const existingPermissionIndex = newPermissions.findIndex(p => p.resource === resource);

    if (existingPermissionIndex >= 0) {
      const existingPermission = newPermissions[existingPermissionIndex];
      if (existingPermission.actions.includes(action)) {
        // Remove action
        existingPermission.actions = existingPermission.actions.filter(a => a !== action);
        if (existingPermission.actions.length === 0) {
          newPermissions.splice(existingPermissionIndex, 1);
        }
      } else {
        // Add action
        existingPermission.actions.push(action);
      }
    } else {
      // Create new permission
      newPermissions.push({ resource, actions: [action] });
    }

    setPermissions(newPermissions);
    setHasChanges(true);
  };

  const handleSave = () => {
    onPermissionsUpdate(permissions);
    setIsEditing(false);
    setHasChanges(false);
  };

  const handleCancel = () => {
    setPermissions(currentPermissions);
    setIsEditing(false);
    setHasChanges(false);
  };

  const isSuperAdmin = userRole === 'super_admin';

  return (
    <div className="bg-white rounded-2xl border border-gray-100 shadow-sm">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 px-6 py-4 border-b border-gray-100 rounded-t-2xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-indigo-100 rounded-xl">
              <ShieldCheckIcon className="h-6 w-6 text-indigo-600" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Permission Management</h3>
              <p className="text-sm text-gray-600">
                {isSuperAdmin ? 'Super Admin - Full Access' : `Manage granular permissions for ${userRole}`}
              </p>
            </div>
          </div>
          
          {isEditable && !isSuperAdmin && (
            <div className="flex items-center space-x-2">
              {isEditing ? (
                <>
                  <button
                    onClick={handleCancel}
                    className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                  >
                    <XMarkIcon className="h-4 w-4 mr-1" />
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={!hasChanges}
                    className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    <CheckIcon className="h-4 w-4 mr-1" />
                    Save Changes
                  </button>
                </>
              ) : (
                <button
                  onClick={() => setIsEditing(true)}
                  className="inline-flex items-center px-3 py-1.5 text-sm font-medium text-indigo-600 bg-indigo-50 border border-indigo-200 rounded-lg hover:bg-indigo-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  <PencilIcon className="h-4 w-4 mr-1" />
                  Edit Permissions
                </button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Super Admin Notice */}
      {isSuperAdmin && (
        <div className="p-4 bg-gradient-to-r from-red-50 to-orange-50 border-b border-gray-100">
          <div className="flex items-center space-x-3">
            <LockOpenIcon className="h-5 w-5 text-red-500" />
            <div>
              <p className="text-sm font-medium text-red-800">Super Administrator Access</p>
              <p className="text-xs text-red-600">This user has unrestricted access to all system resources and cannot be limited.</p>
            </div>
          </div>
        </div>
      )}

      {/* Permissions Grid */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Object.entries(AVAILABLE_RESOURCES).map(([resource, config]) => (
            <div
              key={resource}
              className={`border rounded-xl p-4 transition-all duration-200 ${
                isEditing ? 'border-indigo-200 bg-indigo-50/30' : 'border-gray-200 bg-gray-50/30'
              }`}
            >
              {/* Resource Header */}
              <div className="flex items-center space-x-3 mb-3">
                <span className="text-2xl">{config.icon}</span>
                <div>
                  <h4 className="font-semibold text-gray-900">{config.name}</h4>
                  <p className="text-xs text-gray-600">{config.description}</p>
                </div>
              </div>

              {/* Actions */}
              <div className="space-y-2">
                {config.actions.map(action => {
                  const hasAccess = isSuperAdmin || hasPermission(resource, action);
                  const isClickable = isEditing && !isSuperAdmin;
                  
                  return (
                    <div
                      key={action}
                      className={`flex items-center justify-between p-2 rounded-lg border transition-all duration-200 ${
                        hasAccess
                          ? ACTION_COLORS[action as keyof typeof ACTION_COLORS]
                          : 'bg-gray-50 text-gray-500 border-gray-200'
                      } ${isClickable ? 'cursor-pointer hover:shadow-sm' : ''}`}
                      onClick={() => isClickable && togglePermission(resource, action)}
                    >
                      <div className="flex items-center space-x-2">
                        <div className={`w-4 h-4 rounded border-2 flex items-center justify-center ${
                          hasAccess ? 'bg-current border-current' : 'border-gray-300'
                        }`}>
                          {hasAccess && <CheckIcon className="h-3 w-3 text-white" />}
                        </div>
                        <span className="text-sm font-medium capitalize">{action}</span>
                      </div>
                      <span className="text-xs opacity-75">
                        {ACTION_DESCRIPTIONS[action as keyof typeof ACTION_DESCRIPTIONS]}
                      </span>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Edit Mode Notice */}
        {isEditing && (
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
            <div className="flex items-start space-x-3">
              <InformationCircleIcon className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-blue-800">Permission Editing Mode</p>
                <p className="text-xs text-blue-600 mt-1">
                  Click on any permission to toggle it on or off. Changes will be saved when you click "Save Changes".
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
