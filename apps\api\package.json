{"name": "@rxy/api", "version": "1.0.0", "description": "API server for RxY platform", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "generate-secret": "node -e console.log(require('crypto').randomBytes(32).toString('hex'))", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@rxy/database": "^1.0.0", "@rxy/shared": "^1.0.0", "cloudinary": "^1.41.0", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-async-errors": "^3.1.1", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.1", "openai": "^4.20.1"}, "devDependencies": {"@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.17", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "eslint": "^8.54.0", "jest": "^29.7.0", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.0"}}