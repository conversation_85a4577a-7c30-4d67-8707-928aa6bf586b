{"name": "@rxy/admin", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev -p 3003", "build": "next build", "start": "next start -p 3003", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@rxy/shared": "file:../../packages/shared", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "framer-motion": "^11.5.4", "next": "^14.2.30", "next-auth": "^4.24.7", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "tailwind-merge": "^2.4.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20.14.10", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-config-next": "14.2.5", "postcss": "^8.4.39", "tailwindcss": "^3.4.6", "typescript": "^5.5.3"}}