/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  // App directory is now stable in Next.js 14, no experimental flag needed
  images: {
    domains: ['localhost', 'res.cloudinary.com'],
  },
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  // Remove redirects for now since they don't work with static export
  // Users will access /admin directly
};

module.exports = nextConfig;
