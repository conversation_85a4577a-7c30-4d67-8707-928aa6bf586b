import mongoose, { Document, Schema } from 'mongoose';

export interface INewsletter extends Document {
  email: string;
  name?: string;
  status: 'active' | 'unsubscribed' | 'bounced' | 'pending';
  preferences: string[];
  source: 'website' | 'social_media' | 'referral' | 'import' | 'api';
  tags: string[];
  metadata: {
    userAgent?: string;
    ip?: string;
    referrer?: string;
    subscribedAt: Date;
    location?: {
      country?: string;
      city?: string;
      timezone?: string;
    };
  };
  engagement: {
    totalEmailsSent: number;
    totalEmailsOpened: number;
    totalLinksClicked: number;
    lastOpenedAt?: Date;
    lastClickedAt?: Date;
    averageOpenRate: number;
    averageClickRate: number;
  };
  segments: string[];
  customFields: Map<string, any>;
  unsubscribedAt?: Date;
  unsubscribeReason?: string;
  bounceCount: number;
  lastBounceAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const NewsletterSchema = new Schema<INewsletter>(
  {
    email: {
      type: String,
      required: true,
      trim: true,
      lowercase: true,
      match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
    },
    name: {
      type: String,
      trim: true,
      maxlength: 100,
    },
    status: {
      type: String,
      enum: ['active', 'unsubscribed', 'bounced', 'pending'],
      default: 'active',
    },
    preferences: [
      {
        type: String,
        enum: [
          'blog_updates',
          'project_updates',
          'tech_insights',
          'weekly_digest',
          'announcements',
          'tutorials',
          'case_studies',
          'industry_news',
        ],
      },
    ],
    source: {
      type: String,
      enum: ['website', 'social_media', 'referral', 'import', 'api'],
      default: 'website',
    },
    tags: [
      {
        type: String,
        trim: true,
        lowercase: true,
      },
    ],
    metadata: {
      userAgent: String,
      ip: String,
      referrer: String,
      subscribedAt: {
        type: Date,
        default: Date.now,
      },
      location: {
        country: String,
        city: String,
        timezone: String,
      },
    },
    engagement: {
      totalEmailsSent: {
        type: Number,
        default: 0,
      },
      totalEmailsOpened: {
        type: Number,
        default: 0,
      },
      totalLinksClicked: {
        type: Number,
        default: 0,
      },
      lastOpenedAt: Date,
      lastClickedAt: Date,
      averageOpenRate: {
        type: Number,
        default: 0,
        min: 0,
        max: 1,
      },
      averageClickRate: {
        type: Number,
        default: 0,
        min: 0,
        max: 1,
      },
    },
    segments: [
      {
        type: String,
        trim: true,
      },
    ],
    customFields: {
      type: Map,
      of: Schema.Types.Mixed,
    },
    unsubscribedAt: Date,
    unsubscribeReason: {
      type: String,
      enum: [
        'too_frequent',
        'not_relevant',
        'never_signed_up',
        'technical_issues',
        'privacy_concerns',
        'other',
      ],
    },
    bounceCount: {
      type: Number,
      default: 0,
    },
    lastBounceAt: Date,
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  }
);

// Indexes
NewsletterSchema.index({ email: 1 }, { unique: true });
NewsletterSchema.index({ status: 1, 'metadata.subscribedAt': -1 });
NewsletterSchema.index({ preferences: 1 });
NewsletterSchema.index({ source: 1 });
NewsletterSchema.index({ tags: 1 });
NewsletterSchema.index({ segments: 1 });
NewsletterSchema.index({ 'engagement.averageOpenRate': -1 });
NewsletterSchema.index({ 'engagement.lastOpenedAt': -1 });

// Virtual for engagement score
NewsletterSchema.virtual('engagementScore').get(function () {
  const openWeight = 0.6;
  const clickWeight = 0.4;
  return (
    this.engagement.averageOpenRate * openWeight + this.engagement.averageClickRate * clickWeight
  );
});

// Virtual for subscriber age
NewsletterSchema.virtual('subscriberAge').get(function () {
  return Date.now() - this.metadata.subscribedAt.getTime();
});

// Virtual for is active
NewsletterSchema.virtual('isActive').get(function () {
  return this.status === 'active';
});

// Pre-save middleware
NewsletterSchema.pre('save', function (next) {
  if (this.isModified('status') && this.status === 'unsubscribed' && !this.unsubscribedAt) {
    this.unsubscribedAt = new Date();
  }

  // Update engagement rates
  if (this.engagement.totalEmailsSent > 0) {
    this.engagement.averageOpenRate =
      this.engagement.totalEmailsOpened / this.engagement.totalEmailsSent;
    this.engagement.averageClickRate =
      this.engagement.totalLinksClicked / this.engagement.totalEmailsSent;
  }

  next();
});

// Static methods
NewsletterSchema.statics.findActive = function () {
  return this.find({ status: 'active' }).sort({ 'metadata.subscribedAt': -1 });
};

NewsletterSchema.statics.findByPreference = function (preference: string) {
  return this.find({
    status: 'active',
    preferences: preference,
  }).sort({ 'metadata.subscribedAt': -1 });
};

NewsletterSchema.statics.findHighEngagement = function (threshold: number = 0.5) {
  return this.find({
    status: 'active',
    'engagement.averageOpenRate': { $gte: threshold },
  }).sort({ 'engagement.averageOpenRate': -1 });
};

NewsletterSchema.statics.getStats = function () {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        avgOpenRate: { $avg: '$engagement.averageOpenRate' },
        avgClickRate: { $avg: '$engagement.averageClickRate' },
      },
    },
  ]);
};

NewsletterSchema.statics.getGrowthStats = function (days: number = 30) {
  const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);

  return this.aggregate([
    {
      $match: {
        'metadata.subscribedAt': { $gte: startDate },
      },
    },
    {
      $group: {
        _id: {
          $dateToString: {
            format: '%Y-%m-%d',
            date: '$metadata.subscribedAt',
          },
        },
        count: { $sum: 1 },
      },
    },
    {
      $sort: { _id: 1 },
    },
  ]);
};

export const Newsletter = mongoose.model<INewsletter>('Newsletter', NewsletterSchema);
