const fetch = require('node-fetch');

const API_BASE_URL = 'http://localhost:3002';

async function testLogin() {
  try {
    console.log('🔧 Testing login with admin credentials...');
    
    const loginResponse = await fetch(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'Admin123!@#',
      }),
    });

    console.log('📊 Response status:', loginResponse.status);
    console.log('📊 Response headers:', Object.fromEntries(loginResponse.headers));

    if (!loginResponse.ok) {
      const errorData = await loginResponse.json();
      console.error('❌ Login failed:', errorData);
      return;
    }

    const loginData = await loginResponse.json();
    console.log('✅ Login successful!');
    console.log('👤 User data:', {
      id: loginData.data.user.id,
      email: loginData.data.user.email,
      name: loginData.data.user.name,
      role: loginData.data.user.role,
    });
    console.log('🔑 Access token received:', loginData.data.tokens.accessToken ? 'Yes' : 'No');
    
  } catch (error) {
    console.error('❌ Error testing login:', error.message);
  }
}

// Run the script
testLogin();
